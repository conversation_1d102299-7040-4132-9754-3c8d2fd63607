import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'routes/app_pages.dart';
import 'services/auth_service.dart';
import 'services/affiliate_service.dart';
import 'services/cart_service.dart';
import 'services/wishlist_service.dart';
import 'services/admin_service.dart';
import 'controllers/auth_controller.dart';
import 'controllers/admin_controller.dart';
import 'controllers/affiliate_controller.dart';
import 'controllers/cart_controller.dart';
import 'utils/mouse_event_handler.dart';
import 'config/desktop_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Handle Flutter errors gracefully
  FlutterError.onError = (FlutterErrorDetails details) {
    // Log the error but don't crash the app for mouse tracker assertions
    if (details.exception.toString().contains('mouse_tracker.dart')) {
      debugPrint('Mouse tracker assertion handled: ${details.exception}');
      return;
    }
    FlutterError.presentError(details);
  };

  // تهيئة الخدمات
  await initServices();

  runApp(const MyApp());
}

// تهيئة الخدمات المطلوبة
Future<void> initServices() async {
  // تهيئة خدمة المصادقة
  Get.put(AuthService(), permanent: true);

  // تهيئة خدمة المسوقين
  Get.put(AffiliateService(), permanent: true);

  // تهيئة خدمة سلة التسوق
  Get.put(CartService(), permanent: true);

  // تهيئة خدمة المفضلة
  Get.put(WishlistService(), permanent: true);

  // تهيئة خدمة الإدارة
  Get.put(AdminService(), permanent: true);

  // تهيئة تحكم المصادقة
  Get.put(AuthController(), permanent: true);

  // تهيئة تحكم الإدارة (lazy loading)
  Get.lazyPut<AdminController>(() => AdminController(), fenix: true);

  // تهيئة تحكم المسوقين (lazy loading)
  Get.lazyPut<AffiliateController>(() => AffiliateController(), fenix: true);

  // تهيئة تحكم سلة التسوق (lazy loading)
  Get.lazyPut<CartController>(() => CartController(), fenix: true);
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      title: 'متجر النظارات العصري',
      theme: DesktopConfig.adjustThemeForDesktop(
        ThemeData(
          primarySwatch: Colors.teal,
          useMaterial3: true,
          fontFamily: 'Arial', // يمكن تغييرها لخط عربي أفضل
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontSize: 16),
            bodyMedium: TextStyle(fontSize: 14),
          ),
        ),
      ),
      scrollBehavior: DesktopConfig.scrollBehavior,
      initialRoute: '/', // البدء بالصفحة الرئيسية للضيوف
      getPages: AppPages.routes,
      locale: const Locale('ar', 'SA'), // اللغة العربية
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}
