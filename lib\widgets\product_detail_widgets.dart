import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/product.dart';
import '../utils/mouse_event_handler.dart';

// معرض صور المنتج
class ProductImageGallery extends StatefulWidget {
  final Product product;

  const ProductImageGallery({super.key, required this.product});

  @override
  State<ProductImageGallery> createState() => _ProductImageGalleryState();
}

class _ProductImageGalleryState extends State<ProductImageGallery> {
  int currentImageIndex = 0;

  @override
  Widget build(BuildContext context) {
    final images = [
      widget.product.mainImageUrl,
      ...widget.product.additionalImages,
    ];

    return Column(
      children: [
        // الصورة الرئيسية
        Container(
          height: 300,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(16),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.network(
              images[currentImageIndex],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.shade200,
                  child: const Icon(
                    Icons.image_not_supported,
                    size: 100,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),

        const SizedBox(height: 16),

        // الصور المصغرة
        if (images.length > 1)
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: images.length,
              itemBuilder: (context, index) {
                return SafeGestureDetector(
                  onTap: () => setState(() => currentImageIndex = index),
                  child: Container(
                    width: 80,
                    height: 80,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: currentImageIndex == index
                            ? Colors.teal
                            : Colors.grey.shade300,
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.network(
                        images[index],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.shade200,
                            child: const Icon(Icons.image_not_supported),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}

// معلومات المنتج الأساسية
class ProductBasicInfo extends StatelessWidget {
  final Product product;

  const ProductBasicInfo({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اسم المنتج
        Text(
          product.name,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        // العلامة التجارية
        Row(
          children: [
            const Text(
              'العلامة التجارية: ',
              style: TextStyle(color: Colors.grey),
            ),
            Text(
              product.brand,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.teal,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // التقييم والمراجعات
        Row(
          children: [
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < product.rating.floor()
                      ? Icons.star
                      : index < product.rating
                          ? Icons.star_half
                          : Icons.star_border,
                  color: Colors.amber,
                  size: 20,
                );
              }),
            ),
            const SizedBox(width: 8),
            Text(
              '${product.rating}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 4),
            Text(
              '(${product.reviewCount} مراجعة)',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // السعر
        Row(
          children: [
            Text(
              product.formattedPrice,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            if (product.originalPrice != null && product.originalPrice! > product.price) ...[
              const SizedBox(width: 12),
              Text(
                '${product.originalPrice!.toStringAsFixed(0)} دج',
                style: const TextStyle(
                  fontSize: 18,
                  decoration: TextDecoration.lineThrough,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${(((product.originalPrice! - product.price) / product.originalPrice!) * 100).round()}% خصم',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),

        const SizedBox(height: 16),

        // حالة التوفر
        Row(
          children: [
            Icon(
              product.stockQuantity > 0 ? Icons.check_circle : Icons.cancel,
              color: product.stockQuantity > 0 ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              product.stockQuantity > 0
                  ? 'متوفر في المخزون (${product.stockQuantity} قطعة)'
                  : 'غير متوفر حالياً',
              style: TextStyle(
                color: product.stockQuantity > 0 ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// تفاصيل المنتج المتقدمة
class ProductAdvancedDetails extends StatelessWidget {
  final Product product;

  const ProductAdvancedDetails({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تفاصيل المنتج',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        // الوصف
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الوصف',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                product.description,
                style: const TextStyle(
                  height: 1.5,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // المواصفات
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'المواصفات',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 12),
              _buildSpecRow('العلامة التجارية', product.brand),
              _buildSpecRow('الفئة', product.category),
              _buildSpecRow('اللون', product.color),
              _buildSpecRow('المادة', 'إطار معدني عالي الجودة'),
              _buildSpecRow('الحماية', 'UV400 - حماية 100%'),
              _buildSpecRow('الضمان', 'سنة واحدة'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// قسم المراجعات
class ProductReviews extends StatelessWidget {
  final Product product;

  const ProductReviews({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    // مراجعات تجريبية
    final reviews = [
      {
        'name': 'أحمد محمد',
        'rating': 5,
        'comment': 'منتج ممتاز وجودة عالية، أنصح بالشراء',
        'date': '2024-01-15',
      },
      {
        'name': 'فاطمة علي',
        'rating': 4,
        'comment': 'جميلة جداً ومريحة، لكن التوصيل كان متأخر قليلاً',
        'date': '2024-01-10',
      },
      {
        'name': 'محمد حسن',
        'rating': 5,
        'comment': 'تماماً كما هو موضح في الصور، راضي جداً عن الشراء',
        'date': '2024-01-05',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'آراء العملاء',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => Get.snackbar('المراجعات', 'سيتم فتح جميع المراجعات'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // ملخص التقييمات
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.amber.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amber.shade200),
          ),
          child: Row(
            children: [
              Column(
                children: [
                  Text(
                    product.rating.toString(),
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < product.rating.floor()
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      );
                    }),
                  ),
                  Text(
                    '${product.reviewCount} مراجعة',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  children: List.generate(5, (index) {
                    final stars = 5 - index;
                    final percentage = (stars / 5 * 100).round();
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Text('$stars'),
                          const SizedBox(width: 4),
                          const Icon(Icons.star, size: 12, color: Colors.amber),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LinearProgressIndicator(
                              value: percentage / 100,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text('$percentage%', style: const TextStyle(fontSize: 12)),
                        ],
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // المراجعات
        ...reviews.map((review) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.teal,
                    child: Text(
                      (review['name'] as String)[0],
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          review['name'] as String,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Row(
                          children: [
                            Row(
                              children: List.generate(5, (index) {
                                return Icon(
                                  index < (review['rating'] as int)
                                      ? Icons.star
                                      : Icons.star_border,
                                  color: Colors.amber,
                                  size: 14,
                                );
                              }),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              review['date'] as String,
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                review['comment'] as String,
                style: const TextStyle(height: 1.4),
              ),
            ],
          ),
        )),
      ],
    );
  }
}
