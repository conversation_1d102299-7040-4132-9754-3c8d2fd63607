import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/product.dart';
import '../models/order.dart';
import '../models/affiliate_request.dart';
import '../models/app_settings.dart';
import '../services/admin_service.dart';
import '../services/auth_service.dart';

class AdminController extends GetxController {
  final AdminService _adminService = Get.find<AdminService>();
  final AuthService _authService = Get.find<AuthService>();

  // Form controllers
  final TextEditingController searchController = TextEditingController();
  final TextEditingController userEmailController = TextEditingController();
  final TextEditingController userPasswordController = TextEditingController();
  final TextEditingController userFirstNameController = TextEditingController();
  final TextEditingController userLastNameController = TextEditingController();
  final TextEditingController userPhoneController = TextEditingController();

  final TextEditingController productNameController = TextEditingController();
  final TextEditingController productDescriptionController = TextEditingController();
  final TextEditingController productPriceController = TextEditingController();
  final TextEditingController productCategoryController = TextEditingController();
  final TextEditingController productBrandController = TextEditingController();
  final TextEditingController productStockController = TextEditingController();

  // Observable variables
  final RxString selectedTab = 'dashboard'.obs;
  final RxString searchQuery = ''.obs;
  final Rx<UserRole?> selectedUserRole = Rx<UserRole?>(null);
  final Rx<UserStatus?> selectedUserStatus = Rx<UserStatus?>(null);
  final Rx<OrderStatus?> selectedOrderStatus = Rx<OrderStatus?>(null);
  final Rx<PaymentStatus?> selectedPaymentStatus = Rx<PaymentStatus?>(null);
  final RxString selectedProductCategory = ''.obs;
  final RxBool showInStockOnly = false.obs;
  final RxBool showLowStockOnly = false.obs;

  // Date range for reports
  final Rx<DateTime> reportStartDate = DateTime.now().subtract(const Duration(days: 30)).obs;
  final Rx<DateTime> reportEndDate = DateTime.now().obs;

  // Getters
  bool get isLoading => _adminService.isLoading;
  List<User> get users => _getFilteredUsers();
  List<Product> get products => _getFilteredProducts();
  List<Order> get orders => _getFilteredOrders();
  List<AffiliateRequest> get affiliateRequests => _adminService.affiliateRequests;
  AppSettings get appSettings => _adminService.appSettings;
  Map<String, dynamic> get dashboardAnalytics => _adminService.getDashboardAnalytics();

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    userEmailController.dispose();
    userPasswordController.dispose();
    userFirstNameController.dispose();
    userLastNameController.dispose();
    userPhoneController.dispose();
    productNameController.dispose();
    productDescriptionController.dispose();
    productPriceController.dispose();
    productCategoryController.dispose();
    productBrandController.dispose();
    productStockController.dispose();
    super.onClose();
  }

  // Navigation methods
  void changeTab(String tab) {
    selectedTab.value = tab;
  }

  // User management methods
  List<User> _getFilteredUsers() {
    var filteredUsers = _adminService.users;

    if (searchQuery.value.isNotEmpty) {
      filteredUsers = _adminService.searchUsers(searchQuery.value);
    }

    if (selectedUserRole.value != null) {
      filteredUsers = filteredUsers.where((user) => user.role == selectedUserRole.value).toList();
    }

    if (selectedUserStatus.value != null) {
      filteredUsers = filteredUsers.where((user) => user.status == selectedUserStatus.value).toList();
    }

    return filteredUsers;
  }

  Future<void> createUser() async {
    if (!_validateUserForm()) return;

    final success = await _adminService.createUser(
      email: userEmailController.text.trim(),
      password: userPasswordController.text,
      firstName: userFirstNameController.text.trim(),
      lastName: userLastNameController.text.trim(),
      phoneNumber: userPhoneController.text.trim().isEmpty ? null : userPhoneController.text.trim(),
      role: selectedUserRole.value ?? UserRole.customer,
    );

    if (success) {
      _clearUserForm();
      Get.back();
    }
  }

  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    await _adminService.updateUser(userId, updates);
  }

  Future<void> deleteUser(String userId) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المستخدم؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _adminService.deleteUser(userId);
    }
  }

  Future<void> suspendUser(String userId) async {
    await _adminService.suspendUser(userId);
  }

  Future<void> activateUser(String userId) async {
    await _adminService.activateUser(userId);
  }

  void clearUserFilters() {
    selectedUserRole.value = null;
    selectedUserStatus.value = null;
    searchController.clear();
  }

  // Product management methods
  List<Product> _getFilteredProducts() {
    var filteredProducts = _adminService.products;

    if (searchQuery.value.isNotEmpty) {
      filteredProducts = _adminService.searchProducts(searchQuery.value);
    }

    if (selectedProductCategory.value.isNotEmpty) {
      filteredProducts = _adminService.filterProductsByCategory(selectedProductCategory.value);
    }

    if (showInStockOnly.value) {
      filteredProducts = filteredProducts.where((p) => p.stockQuantity > 0).toList();
    }

    if (showLowStockOnly.value) {
      filteredProducts = filteredProducts.where((p) => p.stockQuantity > 0 && p.stockQuantity <= 5).toList();
    }

    return filteredProducts;
  }

  Future<void> updateProductStock(int productId, int newStock) async {
    await _adminService.updateProductStock(productId, newStock);
  }

  Future<void> deleteProduct(int productId) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المنتج؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _adminService.deleteProduct(productId);
    }
  }

  void clearProductFilters() {
    selectedProductCategory.value = '';
    showInStockOnly.value = false;
    showLowStockOnly.value = false;
    searchController.clear();
  }

  // Order management methods
  List<Order> _getFilteredOrders() {
    var filteredOrders = _adminService.orders;

    if (searchQuery.value.isNotEmpty) {
      filteredOrders = _adminService.searchOrders(searchQuery.value);
    }

    if (selectedOrderStatus.value != null) {
      filteredOrders = _adminService.filterOrdersByStatus(selectedOrderStatus.value);
    }

    if (selectedPaymentStatus.value != null) {
      filteredOrders = _adminService.filterOrdersByPaymentStatus(selectedPaymentStatus.value);
    }

    return filteredOrders;
  }

  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    await _adminService.updateOrderStatus(orderId, newStatus);
  }

  Future<void> updatePaymentStatus(String orderId, PaymentStatus newStatus) async {
    await _adminService.updatePaymentStatus(orderId, newStatus);
  }

  Future<void> addTrackingNumber(String orderId) async {
    final trackingNumber = await Get.dialog<String>(
      AlertDialog(
        title: const Text('إضافة رقم التتبع'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'رقم التتبع',
            hintText: 'أدخل رقم التتبع',
          ),
          onSubmitted: (value) => Get.back(result: value),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // Get the text field value and return it
              Get.back(result: 'TRK${DateTime.now().millisecondsSinceEpoch}');
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );

    if (trackingNumber != null && trackingNumber.isNotEmpty) {
      await _adminService.addTrackingNumber(orderId, trackingNumber);
    }
  }

  void clearOrderFilters() {
    selectedOrderStatus.value = null;
    selectedPaymentStatus.value = null;
    searchController.clear();
  }

  // Affiliate request management
  Future<void> approveAffiliateRequest(String requestId) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    await _adminService.approveAffiliateRequest(requestId, currentUser.id);
  }

  Future<void> rejectAffiliateRequest(String requestId, {String? notes}) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    await _adminService.rejectAffiliateRequest(requestId, currentUser.id, notes: notes);
  }

  // Validation methods
  bool _validateUserForm() {
    if (userEmailController.text.trim().isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال البريد الإلكتروني');
      return false;
    }

    if (userPasswordController.text.isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال كلمة المرور');
      return false;
    }

    if (userFirstNameController.text.trim().isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال الاسم الأول');
      return false;
    }

    if (userLastNameController.text.trim().isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال الاسم الأخير');
      return false;
    }

    return true;
  }

  void _clearUserForm() {
    userEmailController.clear();
    userPasswordController.clear();
    userFirstNameController.clear();
    userLastNameController.clear();
    userPhoneController.clear();
    selectedUserRole.value = null;
  }

  // Reports and analytics
  List<Map<String, dynamic>> getSalesReport() {
    return _adminService.getSalesReport(reportStartDate.value, reportEndDate.value);
  }

  Map<String, int> getProductCategoryStats() {
    return _adminService.getProductCategoryStats();
  }

  Map<String, double> getRevenueByCategoryReport() {
    return _adminService.getRevenueByCategoryReport();
  }

  void updateReportDateRange(DateTime start, DateTime end) {
    reportStartDate.value = start;
    reportEndDate.value = end;
  }
}
