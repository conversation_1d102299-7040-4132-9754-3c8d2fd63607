import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/wishlist.dart';
import '../../models/product.dart';
import '../../services/wishlist_service.dart';
import '../../widgets/user_role_widgets.dart';
import '../../screens/product/product_detail_screen.dart';

class WishlistScreen extends StatelessWidget {
  const WishlistScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final wishlistService = Get.find<WishlistService>();

    return Scaffold(
      appBar: const UserRoleAppBar(title: 'المفضلة'),
      body: Obx(() {
        final wishlist = wishlistService.wishlist;

        if (wishlist == null || wishlist.items.isEmpty) {
          return _buildEmptyWishlist();
        }

        return Column(
          children: [
            // إحصائيات المفضلة
            _buildWishlistStats(wishlistService),

            // قائمة المنتجات
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: wishlist.items.length,
                itemBuilder: (context, index) {
                  final item = wishlist.items[index];
                  return _buildWishlistItem(item, wishlistService);
                },
              ),
            ),

            // أزرار الإجراءات
            if (wishlist.items.isNotEmpty) _buildActionButtons(wishlistService),
          ],
        );
      }),
    );
  }

  Widget _buildEmptyWishlist() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 100,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'المفضلة فارغة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف منتجات لتحفظها هنا',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: const Text('تصفح المنتجات'),
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistStats(WishlistService wishlistService) {
    return Obx(() {
      final stats = wishlistService.getWishlistStats();

      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.pink.shade400, Colors.red.shade400],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(Icons.favorite, color: Colors.white, size: 30),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${stats['totalItems']} منتج في المفضلة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${stats['availableItems']} متوفر • ${stats['unavailableItems']} غير متوفر',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'القيمة الإجمالية',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
                Text(
                  '${stats['totalValue'].toStringAsFixed(0)} دج',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildWishlistItem(WishlistItem item, WishlistService wishlistService) {
    final isAvailable = item.product.stockQuantity > 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => Get.to(() => ProductDetailScreen(product: item.product)),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // صورة المنتج
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      item.product.mainImageUrl,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.image_not_supported),
                        );
                      },
                    ),
                  ),
                  if (!isAvailable)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Text(
                          'غير متوفر',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 12),

              // معلومات المنتج
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.product.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isAvailable ? Colors.black : Colors.grey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.product.brand,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          item.product.formattedPrice,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isAvailable ? Colors.teal : Colors.grey,
                          ),
                        ),
                        const Spacer(),
                        // تقييم المنتج
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.amber[600],
                            ),
                            const SizedBox(width: 2),
                            Text(
                              item.product.rating.toString(),
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'أضيف في ${_formatDate(item.addedAt)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              Column(
                children: [
                  IconButton(
                    onPressed: () => wishlistService.removeFromWishlist(item.product.id),
                    icon: const Icon(Icons.favorite, color: Colors.red),
                    tooltip: 'حذف من المفضلة',
                  ),
                  if (isAvailable)
                    IconButton(
                      onPressed: () => _addToCart(item.product),
                      icon: const Icon(Icons.add_shopping_cart, color: Colors.teal),
                      tooltip: 'أضف للسلة',
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(WishlistService wishlistService) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showClearConfirmation(wishlistService),
              icon: const Icon(Icons.clear_all),
              label: const Text('تفريغ المفضلة'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => wishlistService.moveAllToCart(),
              icon: const Icon(Icons.shopping_cart),
              label: const Text('نقل الكل للسلة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addToCart(Product product) {
    // هنا يمكن إضافة منطق إضافة المنتج للسلة
    Get.snackbar(
      'تمت الإضافة',
      'تم إضافة ${product.name} إلى السلة',
      snackPosition: SnackPosition.TOP,
    );
  }

  void _showClearConfirmation(WishlistService wishlistService) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد التفريغ'),
        content: const Text('هل أنت متأكد من تفريغ المفضلة؟ سيتم حذف جميع المنتجات.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              wishlistService.clearWishlist();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
