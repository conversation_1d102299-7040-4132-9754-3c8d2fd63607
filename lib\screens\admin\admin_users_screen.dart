import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../models/user.dart';
import '../../config/desktop_config.dart';
import 'forms/add_edit_user_form.dart';

class AdminUsersScreen extends StatelessWidget {
  const AdminUsersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final adminController = Get.find<AdminController>();
    final isMobile = DesktopConfig.isMobileSize(context);
    final padding = DesktopConfig.getResponsivePadding(context);

    return Scaffold(
      body: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدارة المستخدمين',
                      style: TextStyle(
                        fontSize: isMobile ? 24 : 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => Get.to(() => const AddEditUserForm()),
                        icon: const Icon(Icons.person_add),
                        label: const Text('إضافة مستخدم جديد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'إدارة المستخدمين',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => Get.to(() => const AddEditUserForm()),
                      icon: const Icon(Icons.person_add),
                      label: const Text('إضافة مستخدم جديد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),

            const SizedBox(height: 24),

            // Statistics Cards
            _buildStatisticsCards(adminController),

            const SizedBox(height: 24),

            // Filters and Search
            _buildFiltersSection(adminController),

            const SizedBox(height: 24),

            // Users Table
            Expanded(
              child: _buildUsersTable(adminController),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(AdminController controller) {
    return Obx(() {
      final analytics = controller.dashboardAnalytics;

      return Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المستخدمين',
              '${analytics['totalUsers']}',
              Icons.people,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'العملاء',
              '${analytics['totalCustomers']}',
              Icons.person,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'المسوقون',
              '${analytics['totalAffiliates']}',
              Icons.group,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'المديرون',
              '${analytics['totalAdmins']}',
              Icons.admin_panel_settings,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'المستخدمون النشطون',
              '${analytics['activeUsers']}',
              Icons.check_circle,
              Colors.teal,
            ),
          ),
        ],
      );
    });
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection(AdminController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                // Search Field
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: controller.searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في المستخدمين',
                      hintText: 'ابحث بالاسم أو البريد الإلكتروني أو رقم الهاتف',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Role Filter
                Expanded(
                  child: Obx(() => DropdownButtonFormField<UserRole?>(
                    value: controller.selectedUserRole.value,
                    decoration: const InputDecoration(
                      labelText: 'تصفية حسب الدور',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.filter_list),
                    ),
                    items: [
                      const DropdownMenuItem<UserRole?>(
                        value: null,
                        child: Text('جميع الأدوار'),
                      ),
                      ...UserRole.values.map((role) => DropdownMenuItem(
                        value: role,
                        child: Text(_getRoleText(role)),
                      )),
                    ],
                    onChanged: (value) {
                      controller.selectedUserRole.value = value;
                    },
                  )),
                ),

                const SizedBox(width: 16),

                // Status Filter
                Expanded(
                  child: Obx(() => DropdownButtonFormField<UserStatus?>(
                    value: controller.selectedUserStatus.value,
                    decoration: const InputDecoration(
                      labelText: 'تصفية حسب الحالة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.toggle_on),
                    ),
                    items: [
                      const DropdownMenuItem<UserStatus?>(
                        value: null,
                        child: Text('جميع الحالات'),
                      ),
                      ...UserStatus.values.map((status) => DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusText(status)),
                      )),
                    ],
                    onChanged: (value) {
                      controller.selectedUserStatus.value = value;
                    },
                  )),
                ),

                const SizedBox(width: 16),

                // Clear Filters Button
                ElevatedButton.icon(
                  onPressed: controller.clearUserFilters,
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الفلاتر'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersTable(AdminController controller) {
    return Obx(() {
      final users = controller.users;

      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (users.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.people_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد مستخدمين',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return Card(
        child: SingleChildScrollView(
          child: DataTable(
            headingRowColor: MaterialStateProperty.all(Colors.grey[100]),
            columns: const [
              DataColumn(label: Text('الصورة')),
              DataColumn(label: Text('الاسم')),
              DataColumn(label: Text('البريد الإلكتروني')),
              DataColumn(label: Text('رقم الهاتف')),
              DataColumn(label: Text('الدور')),
              DataColumn(label: Text('الحالة')),
              DataColumn(label: Text('تاريخ التسجيل')),
              DataColumn(label: Text('الإجراءات')),
            ],
            rows: users.map((user) => DataRow(
              cells: [
                // Profile Image
                DataCell(
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: _getRoleColor(user.role),
                    backgroundImage: user.profileImageUrl != null
                        ? NetworkImage(user.profileImageUrl!)
                        : null,
                    child: user.profileImageUrl == null
                        ? Text(
                            user.firstName[0].toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                ),

                // Name
                DataCell(
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        user.fullName,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      if (user.isAffiliate && user.affiliateCode != null)
                        Text(
                          'كود المسوق: ${user.affiliateCode}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),

                // Email
                DataCell(Text(user.email)),

                // Phone
                DataCell(Text(user.phoneNumber ?? 'غير محدد')),

                // Role
                DataCell(
                  Chip(
                    label: Text(_getRoleText(user.role)),
                    backgroundColor: _getRoleColor(user.role).withOpacity(0.1),
                    labelStyle: TextStyle(color: _getRoleColor(user.role)),
                    avatar: Icon(
                      _getRoleIcon(user.role),
                      color: _getRoleColor(user.role),
                      size: 16,
                    ),
                  ),
                ),

                // Status
                DataCell(
                  Chip(
                    label: Text(_getStatusText(user.status)),
                    backgroundColor: _getStatusColor(user.status).withOpacity(0.1),
                    labelStyle: TextStyle(color: _getStatusColor(user.status)),
                    avatar: Icon(
                      _getStatusIcon(user.status),
                      color: _getStatusColor(user.status),
                      size: 16,
                    ),
                  ),
                ),

                // Created Date
                DataCell(
                  Text(
                    '${user.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}',
                  ),
                ),

                // Actions
                DataCell(
                  Builder(
                    builder: (context) {
                      final screenWidth = MediaQuery.of(context).size.width;
                      final isMobile = screenWidth < 600;
                      final iconSize = isMobile ? 28.0 : 24.0;

                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () => Get.to(() => AddEditUserForm(user: user)),
                            icon: Icon(Icons.edit, size: iconSize),
                            tooltip: 'تعديل',
                            color: Colors.blue,
                            padding: EdgeInsets.all(isMobile ? 12 : 8),
                          ),
                          if (user.isActive)
                            IconButton(
                              onPressed: () => controller.suspendUser(user.id),
                              icon: Icon(Icons.block, size: iconSize),
                              tooltip: 'إيقاف',
                              color: Colors.orange,
                              padding: EdgeInsets.all(isMobile ? 12 : 8),
                            )
                          else
                            IconButton(
                              onPressed: () => controller.activateUser(user.id),
                              icon: Icon(Icons.check_circle, size: iconSize),
                              tooltip: 'تفعيل',
                              color: Colors.green,
                              padding: EdgeInsets.all(isMobile ? 12 : 8),
                            ),
                          IconButton(
                            onPressed: () => _showDeleteConfirmation(controller, user),
                            icon: Icon(Icons.delete, size: iconSize),
                            tooltip: 'حذف',
                            color: Colors.red,
                            padding: EdgeInsets.all(isMobile ? 12 : 8),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            )).toList(),
          ),
        ),
      );
    });
  }

  // Helper methods
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير';
      case UserRole.affiliate:
        return 'مسوق';
      case UserRole.customer:
        return 'عميل';
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.affiliate:
        return Colors.orange;
      case UserRole.customer:
        return Colors.blue;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.affiliate:
        return Icons.group;
      case UserRole.customer:
        return Icons.person;
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'موقوف';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.inactive:
        return Colors.grey;
      case UserStatus.suspended:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Icons.check_circle;
      case UserStatus.inactive:
        return Icons.pause_circle;
      case UserStatus.suspended:
        return Icons.block;
    }
  }

  void _showDeleteConfirmation(AdminController controller, User user) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\n\n'
          'هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بهذا المستخدم.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteUser(user.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}