import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../models/user.dart';
import '../models/commission.dart';
import '../models/affiliate_link.dart';
import '../services/affiliate_service.dart';
import '../services/auth_service.dart';

class AffiliateController extends GetxController with GetTickerProviderStateMixin {
  final AffiliateService _affiliateService = Get.find<AffiliateService>();
  final AuthService _authService = Get.find<AuthService>();
  
  // Tab Controller
  late TabController tabController;
  
  // Observable variables
  final RxBool isLoading = false.obs;
  final RxList<Commission> commissions = <Commission>[].obs;
  final RxList<AffiliateLink> affiliateLinks = <AffiliateLink>[].obs;
  final RxMap<String, dynamic> stats = <String, dynamic>{}.obs;
  
  // Form controllers for creating links
  final TextEditingController productUrlController = TextEditingController();
  final TextEditingController customUrlController = TextEditingController();
  final Rx<LinkType> selectedLinkType = LinkType.general.obs;
  
  // Filters
  final Rx<CommissionStatus?> statusFilter = Rx<CommissionStatus?>(null);
  final Rx<DateTimeRange?> dateFilter = Rx<DateTimeRange?>(null);

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);
    loadData();
  }

  @override
  void onClose() {
    tabController.dispose();
    productUrlController.dispose();
    customUrlController.dispose();
    super.onClose();
  }

  // تحميل البيانات
  Future<void> loadData() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;
    
    isLoading.value = true;
    
    try {
      final affiliateId = _authService.currentUser!.id;
      
      // تحميل العمولات
      commissions.value = _affiliateService.getAffiliateCommissions(affiliateId);
      
      // تحميل الروابط التسويقية
      affiliateLinks.value = _affiliateService.getAffiliateLinks(affiliateId);
      
      // تحميل الإحصائيات
      stats.value = _affiliateService.getAffiliateStats(affiliateId);
      
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // إنشاء رابط تسويقي جديد
  Future<void> createAffiliateLink() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;
    
    final user = _authService.currentUser!;
    
    try {
      isLoading.value = true;
      
      String? productId;
      String? customUrl;
      
      if (selectedLinkType.value == LinkType.product) {
        // استخراج معرف المنتج من الرابط
        productId = _extractProductIdFromUrl(productUrlController.text);
        if (productId == null) {
          Get.snackbar('خطأ', 'رابط المنتج غير صحيح');
          return;
        }
      } else if (selectedLinkType.value == LinkType.general) {
        customUrl = customUrlController.text.trim();
      }
      
      final link = await _affiliateService.createAffiliateLink(
        affiliateId: user.id,
        affiliateCode: user.affiliateCode!,
        type: selectedLinkType.value,
        productId: productId,
        customUrl: customUrl,
      );
      
      affiliateLinks.add(link);
      
      // تنظيف النماذج
      productUrlController.clear();
      customUrlController.clear();
      selectedLinkType.value = LinkType.general;
      
      Get.back(); // إغلاق الحوار
      
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء إنشاء الرابط: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // نسخ الرابط إلى الحافظة
  void copyLinkToClipboard(String url) {
    Clipboard.setData(ClipboardData(text: url));
    Get.snackbar(
      'تم النسخ',
      'تم نسخ الرابط إلى الحافظة',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  // مشاركة الرابط
  void shareLink(String url, String title) {
    // في التطبيق الحقيقي، استخدم share package
    copyLinkToClipboard(url);
    Get.snackbar(
      'مشاركة الرابط',
      'تم نسخ الرابط للمشاركة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // طلب صرف العمولات
  Future<void> requestPayout() async {
    if (_authService.currentUser?.role != UserRole.affiliate) return;
    
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('طلب صرف العمولات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المبلغ المستحق: \$${stats['pendingEarnings']?.toStringAsFixed(2) ?? '0.00'}'),
            const SizedBox(height: 8),
            const Text('هل تريد طلب صرف العمولات المستحقة؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      isLoading.value = true;
      final success = await _affiliateService.requestPayout(_authService.currentUser!.id);
      isLoading.value = false;
      
      if (success) {
        loadData(); // إعادة تحميل البيانات
      }
    }
  }

  // تطبيق فلتر الحالة
  void applyStatusFilter(CommissionStatus? status) {
    statusFilter.value = status;
    _applyFilters();
  }

  // تطبيق فلتر التاريخ
  void applyDateFilter(DateTimeRange? dateRange) {
    dateFilter.value = dateRange;
    _applyFilters();
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    if (_authService.currentUser?.role != UserRole.affiliate) return;
    
    var filteredCommissions = _affiliateService.getAffiliateCommissions(_authService.currentUser!.id);
    
    // فلتر الحالة
    if (statusFilter.value != null) {
      filteredCommissions = filteredCommissions
          .where((c) => c.status == statusFilter.value)
          .toList();
    }
    
    // فلتر التاريخ
    if (dateFilter.value != null) {
      filteredCommissions = filteredCommissions
          .where((c) => 
              c.createdAt.isAfter(dateFilter.value!.start) &&
              c.createdAt.isBefore(dateFilter.value!.end.add(const Duration(days: 1))))
          .toList();
    }
    
    commissions.value = filteredCommissions;
  }

  // مسح الفلاتر
  void clearFilters() {
    statusFilter.value = null;
    dateFilter.value = null;
    loadData();
  }

  // استخراج معرف المنتج من الرابط
  String? _extractProductIdFromUrl(String url) {
    // تنفيذ بسيط لاستخراج معرف المنتج
    final regex = RegExp(r'/product/(\d+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  // الحصول على نص حالة العمولة
  String getCommissionStatusText(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return 'معلقة';
      case CommissionStatus.approved:
        return 'معتمدة';
      case CommissionStatus.processing:
        return 'قيد المعالجة';
      case CommissionStatus.earned:
        return 'مستحقة';
      case CommissionStatus.paid:
        return 'مدفوعة';
      case CommissionStatus.cancelled:
        return 'ملغية';
      case CommissionStatus.refunded:
        return 'مسترجعة';
    }
  }

  // الحصول على لون حالة العمولة
  Color getCommissionStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return Colors.orange;
      case CommissionStatus.approved:
        return Colors.blue;
      case CommissionStatus.processing:
        return Colors.purple;
      case CommissionStatus.earned:
        return Colors.green;
      case CommissionStatus.paid:
        return Colors.teal;
      case CommissionStatus.cancelled:
        return Colors.red;
      case CommissionStatus.refunded:
        return Colors.grey;
    }
  }

  // الحصول على نص نوع الرابط
  String getLinkTypeText(LinkType type) {
    switch (type) {
      case LinkType.product:
        return 'منتج محدد';
      case LinkType.category:
        return 'فئة منتجات';
      case LinkType.general:
        return 'عام';
    }
  }
}
