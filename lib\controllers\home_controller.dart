import 'package:get/get.dart';
import '../models/product.dart';

class HomeController extends GetxController {
  // Observable list of products
  final RxList<Product> products = <Product>[].obs;
  final RxList<Product> popularProducts = <Product>[].obs;
  final RxList<Product> recommendedProducts = <Product>[].obs;

  // Loading state
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadProducts();
  }

  // Load products (simulate API call)
  void loadProducts() {
    isLoading.value = true;

    // Simulate network delay
    Future.delayed(const Duration(milliseconds: 500), () {
      products.value = Product.demoProducts;
      popularProducts.value = Product.popularProducts;
      recommendedProducts.value = Product.recommendedProducts;
      isLoading.value = false;
    });
  }

  // Refresh products
  void refreshProducts() {
    loadProducts();
  }

  // Search products by name
  List<Product> searchProducts(String query) {
    if (query.isEmpty) return products;
    return products.where((product) =>
        product.name.toLowerCase().contains(query.toLowerCase()) ||
        product.description.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}