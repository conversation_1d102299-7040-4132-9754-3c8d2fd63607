import 'product.dart';

class WishlistItem {
  final Product product;
  final DateTime addedAt;

  WishlistItem({
    required this.product,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'product': product.toJson(),
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory WishlistItem.fromJson(Map<String, dynamic> json) {
    return WishlistItem(
      product: Product.fromJson(json['product']),
      addedAt: DateTime.parse(json['addedAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class Wishlist {
  final String userId;
  final List<WishlistItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  Wishlist({
    required this.userId,
    required this.items,
    required this.createdAt,
    required this.updatedAt,
  });

  // التحقق من وجود منتج في المفضلة
  bool hasProduct(int productId) {
    return items.any((item) => item.product.id == productId);
  }

  // الحصول على عنصر من المفضلة
  WishlistItem? getItem(int productId) {
    try {
      return items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  // إضافة منتج للمفضلة
  Wishlist addItem(Product product) {
    if (hasProduct(product.id)) {
      return this; // المنتج موجود بالفعل
    }

    final updatedItems = List<WishlistItem>.from(items);
    updatedItems.add(WishlistItem(
      product: product,
      addedAt: DateTime.now(),
    ));

    return copyWith(
      items: updatedItems,
      updatedAt: DateTime.now(),
    );
  }

  // حذف منتج من المفضلة
  Wishlist removeItem(int productId) {
    final updatedItems = items.where((item) => item.product.id != productId).toList();
    
    return copyWith(
      items: updatedItems,
      updatedAt: DateTime.now(),
    );
  }

  // تفريغ المفضلة
  Wishlist clear() {
    return copyWith(
      items: [],
      updatedAt: DateTime.now(),
    );
  }

  // عدد العناصر
  int get itemCount => items.length;

  // المنتجات المتوفرة في المخزون
  List<WishlistItem> get availableItems {
    return items.where((item) => item.product.stockQuantity > 0).toList();
  }

  // المنتجات غير المتوفرة
  List<WishlistItem> get unavailableItems {
    return items.where((item) => item.product.stockQuantity == 0).toList();
  }

  Wishlist copyWith({
    String? userId,
    List<WishlistItem>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Wishlist(
      userId: userId ?? this.userId,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'items': items.map((item) => item.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Wishlist.fromJson(Map<String, dynamic> json) {
    return Wishlist(
      userId: json['userId'] ?? '',
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => WishlistItem.fromJson(item))
          .toList() ?? [],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  // إنشاء مفضلة فارغة
  factory Wishlist.empty(String userId) {
    final now = DateTime.now();
    return Wishlist(
      userId: userId,
      items: [],
      createdAt: now,
      updatedAt: now,
    );
  }
}
