import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

/// Desktop-specific configuration for better mouse handling
class DesktopConfig {
  static bool get isDesktop {
    return defaultTargetPlatform == TargetPlatform.windows ||
           defaultTargetPlatform == TargetPlatform.macOS ||
           defaultTargetPlatform == TargetPlatform.linux;
  }

  static bool get isWindows => defaultTargetPlatform == TargetPlatform.windows;
  static bool get isMacOS => defaultTargetPlatform == TargetPlatform.macOS;
  static bool get isLinux => defaultTargetPlatform == TargetPlatform.linux;

  /// Get platform-specific scroll behavior
  static ScrollBehavior get scrollBehavior {
    if (isDesktop) {
      return const MaterialScrollBehavior().copyWith(
        dragDevices: {
          PointerDeviceKind.mouse,
          PointerDeviceKind.touch,
          PointerDeviceKind.stylus,
          PointerDeviceKind.trackpad,
        },
      );
    }
    return const MaterialScrollBehavior();
  }

  /// Get platform-specific theme adjustments
  static ThemeData adjustThemeForDesktop(ThemeData theme) {
    if (!isDesktop) return theme;

    return theme.copyWith(
      // Adjust hover colors for desktop
      hoverColor: theme.primaryColor.withOpacity(0.04),
      focusColor: theme.primaryColor.withOpacity(0.12),

      // Better button themes for desktop
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          minimumSize: const Size(120, 40),
        ),
      ),

      // Better text button themes for desktop
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          minimumSize: const Size(80, 36),
        ),
      ),

      // Better icon button themes for desktop
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          minimumSize: const Size(40, 40),
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  /// Get desktop-specific window constraints
  static BoxConstraints get windowConstraints {
    return const BoxConstraints(
      minWidth: 800,
      minHeight: 600,
      maxWidth: double.infinity,
      maxHeight: double.infinity,
    );
  }

  /// Desktop-specific app bar height
  static double get appBarHeight => isDesktop ? 56.0 : kToolbarHeight;

  /// Desktop-specific navigation rail width
  static double get navigationRailWidth => 72.0;

  /// Desktop-specific breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  /// Check if screen size is mobile
  static bool isMobileSize(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if screen size is tablet
  static bool isTabletSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if screen size is desktop
  static bool isDesktopSize(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobileSize(context)) {
      return const EdgeInsets.all(16);
    } else if (isTabletSize(context)) {
      return const EdgeInsets.all(24);
    } else {
      return const EdgeInsets.all(32);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobileSize(context)) {
      return const EdgeInsets.all(8);
    } else if (isTabletSize(context)) {
      return const EdgeInsets.all(12);
    } else {
      return const EdgeInsets.all(16);
    }
  }
}
