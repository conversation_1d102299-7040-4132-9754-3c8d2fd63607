import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user.dart';
import '../../widgets/user_role_widgets.dart';
import '../../widgets/home_widgets.dart';
import '../../services/auth_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UserRoleAppBar(title: "متجر النظارات العصري"),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // شريط الإعلانات المتحرك
            const AnnouncementBanner(),

            // رسالة ترحيب للمسوقين
            Obx(() {
              final authService = Get.find<AuthService>();
              final user = authService.currentUser;

              if (user != null && user.role == UserRole.affiliate) {
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.orange.shade400, Colors.orange.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.trending_up, color: Colors.white, size: 30),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً ${user.firstName}! 👋',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              'انقر على أي منتج لإنشاء رابط تسويقي وكسب العمولات',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextButton(
                        onPressed: () => Get.toNamed('/settings'),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.white.withOpacity(0.2),
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('الإعدادات'),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox();
            }),

            // بانر الترويج الرئيسي
            const HeroBanner(),

            // قسم الفئات
            const CategoriesSection(),

            // قسم المنتجات المميزة
            const FeaturedProductsSection(),

            // قسم المزايا
            const FeaturesSection(),

            // فوتر
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFooterColumn('المتجر', [
                'من نحن',
                'اتصل بنا',
                'الأسئلة الشائعة',
              ]),
              _buildFooterColumn('الخدمات', [
                'الشحن والتوصيل',
                'الإرجاع والاستبدال',
                'الضمان',
              ]),
              _buildFooterColumn('الحساب', [
                'تسجيل الدخول',
                'إنشاء حساب',
                'طلباتي',
              ]),
            ],
          ),
          const SizedBox(height: 24),
          const Divider(color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            '© 2024 متجر النظارات العصري - جميع الحقوق محفوظة',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'صنع بـ ❤️ في الجزائر',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFooterColumn(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: GestureDetector(
            onTap: () => Get.snackbar('قريباً', 'هذه الميزة ستكون متاحة قريباً'),
            child: Text(
              item,
              style: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
            ),
          ),
        )),
      ],
    );
  }
}
