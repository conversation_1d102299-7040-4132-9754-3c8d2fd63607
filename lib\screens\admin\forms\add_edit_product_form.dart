import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../models/product.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/admin_service.dart';

class AddEditProductForm extends StatefulWidget {
  final Product? product; // null للإضافة، Product للتعديل

  const AddEditProductForm({super.key, this.product});

  @override
  State<AddEditProductForm> createState() => _AddEditProductFormState();
}

class _AddEditProductFormState extends State<AddEditProductForm> {
  final _formKey = GlobalKey<FormState>();
  final AdminController _adminController = Get.find<AdminController>();

  // Form controllers
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  late TextEditingController _categoryController;
  late TextEditingController _brandController;
  late TextEditingController _stockController;
  late TextEditingController _frameWidthController;
  late TextEditingController _frameLengthController;
  late TextEditingController _lensWidthController;
  late TextEditingController _lensHeightController;
  late TextEditingController _bridgeWidthController;
  late TextEditingController _templeWidthController;

  // Form state
  EyewearType _selectedType = EyewearType.sunglasses;
  Gender _selectedGender = Gender.unisex;
  bool _isActive = true;
  bool _isLoading = false;
  List<String> _imageUrls = [];
  List<String> _features = [];
  List<String> _tags = [];

  // For editing
  bool get isEditing => widget.product != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    if (isEditing) {
      final product = widget.product!;
      _nameController = TextEditingController(text: product.name);
      _descriptionController = TextEditingController(text: product.description);
      _priceController = TextEditingController(text: product.price.toString());
      _categoryController = TextEditingController(text: product.category);
      _brandController = TextEditingController(text: product.brand);
      _stockController = TextEditingController(text: product.stockQuantity.toString());
      _frameWidthController = TextEditingController(text: '');
      _frameLengthController = TextEditingController(text: '');
      _lensWidthController = TextEditingController(text: product.specs.lensWidth.toString());
      _lensHeightController = TextEditingController(text: '');
      _bridgeWidthController = TextEditingController(text: product.specs.bridgeWidth.toString());
      _templeWidthController = TextEditingController(text: product.specs.templeLength.toString());

      _selectedType = product.type;
      _selectedGender = product.targetGender;
      _isActive = product.isActive;
      _imageUrls = List.from(product.imageUrls);
      _features = List.from(product.features);
      _tags = List.from(product.tags);
    } else {
      _nameController = TextEditingController();
      _descriptionController = TextEditingController();
      _priceController = TextEditingController();
      _categoryController = TextEditingController();
      _brandController = TextEditingController();
      _stockController = TextEditingController();
      _frameWidthController = TextEditingController();
      _frameLengthController = TextEditingController();
      _lensWidthController = TextEditingController();
      _lensHeightController = TextEditingController();
      _bridgeWidthController = TextEditingController();
      _templeWidthController = TextEditingController();

      // Default image URLs for new products
      _imageUrls = [
        'https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=400',
      ];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _categoryController.dispose();
    _brandController.dispose();
    _stockController.dispose();
    _frameWidthController.dispose();
    _frameLengthController.dispose();
    _lensWidthController.dispose();
    _lensHeightController.dispose();
    _bridgeWidthController.dispose();
    _templeWidthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          if (isEditing)
            IconButton(
              onPressed: _showDeleteConfirmation,
              icon: const Icon(Icons.delete),
              tooltip: 'حذف المنتج',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Card
                    _buildHeaderCard(),

                    const SizedBox(height: 24),

                    // Basic Information
                    _buildBasicInfoSection(),

                    const SizedBox(height: 24),

                    // Product Details
                    _buildProductDetailsSection(),

                    const SizedBox(height: 24),

                    // Specifications
                    _buildSpecificationsSection(),

                    const SizedBox(height: 24),

                    // Images
                    _buildImagesSection(),

                    const SizedBox(height: 24),

                    // Features and Tags
                    _buildFeaturesTagsSection(),

                    const SizedBox(height: 32),

                    // Action Buttons
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                isEditing ? Icons.edit : Icons.add_box,
                color: Colors.green,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isEditing ? 'تعديل بيانات المنتج' : 'إضافة منتج جديد',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isEditing
                        ? 'تعديل بيانات المنتج الحالي'
                        : 'إضافة منتج جديد إلى المتجر',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            if (isEditing)
              Switch(
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: Colors.green,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.shopping_bag),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المنتج';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف المنتج';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر (دج) *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال السعر';
                      }
                      final price = double.tryParse(value);
                      if (price == null || price <= 0) {
                        return 'يرجى إدخال سعر صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _stockController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية المتوفرة *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.inventory),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الكمية';
                      }
                      final stock = int.tryParse(value);
                      if (stock == null || stock < 0) {
                        return 'يرجى إدخال كمية صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المنتج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _categoryController,
                    decoration: const InputDecoration(
                      labelText: 'الفئة *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الفئة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _brandController,
                    decoration: const InputDecoration(
                      labelText: 'العلامة التجارية *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.branding_watermark),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال العلامة التجارية';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نوع المنتج *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<EyewearType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.visibility),
                        ),
                        items: EyewearType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Icon(
                                  _getTypeIcon(type),
                                  color: _getTypeColor(type),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(_getTypeText(type)),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الجنس المستهدف *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<Gender>(
                        value: _selectedGender,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.people),
                        ),
                        items: Gender.values.map((gender) {
                          return DropdownMenuItem(
                            value: gender,
                            child: Row(
                              children: [
                                Icon(
                                  _getGenderIcon(gender),
                                  color: _getGenderColor(gender),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(_getGenderText(gender)),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedGender = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecificationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المواصفات التقنية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _frameWidthController,
                    decoration: const InputDecoration(
                      labelText: 'عرض الإطار (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _frameLengthController,
                    decoration: const InputDecoration(
                      labelText: 'طول الإطار (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _lensWidthController,
                    decoration: const InputDecoration(
                      labelText: 'عرض العدسة (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.lens),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _lensHeightController,
                    decoration: const InputDecoration(
                      labelText: 'ارتفاع العدسة (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.lens),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _bridgeWidthController,
                    decoration: const InputDecoration(
                      labelText: 'عرض الجسر (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.architecture),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _templeWidthController,
                    decoration: const InputDecoration(
                      labelText: 'عرض الذراع (مم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'صور المنتج',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addImageUrl,
                  icon: const Icon(Icons.add_photo_alternate),
                  label: const Text('إضافة صورة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_imageUrls.isEmpty)
              Container(
                height: 100,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text(
                    'لا توجد صور مضافة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _imageUrls.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _imageUrls[index],
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 100,
                                  height: 100,
                                  color: Colors.grey[300],
                                  child: const Icon(Icons.image_not_supported),
                                );
                              },
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeImageUrl(index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesTagsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الميزات والعلامات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Features
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الميزات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addFeature,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة ميزة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (_features.isEmpty)
              const Text(
                'لا توجد ميزات مضافة',
                style: TextStyle(color: Colors.grey),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _features.map((feature) {
                  return Chip(
                    label: Text(feature),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeFeature(feature),
                    backgroundColor: Colors.orange.withOpacity(0.1),
                  );
                }).toList(),
              ),

            const SizedBox(height: 16),

            // Tags
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'العلامات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addTag,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة علامة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (_tags.isEmpty)
              const Text(
                'لا توجد علامات مضافة',
                style: TextStyle(color: Colors.grey),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeTag(tag),
                    backgroundColor: Colors.purple.withOpacity(0.1),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Get.back(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('إلغاء'),
          ),
        ),

        const SizedBox(width: 16),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProduct,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(isEditing ? 'حفظ التعديلات' : 'إضافة المنتج'),
          ),
        ),
      ],
    );
  }

  // Helper methods for type and gender
  IconData _getTypeIcon(EyewearType type) {
    switch (type) {
      case EyewearType.sunglasses:
        return Icons.wb_sunny;
      case EyewearType.prescription:
        return Icons.visibility;
      case EyewearType.reading:
        return Icons.menu_book;
      case EyewearType.sports:
        return Icons.sports;
      case EyewearType.fashion:
        return Icons.style;
    }
  }

  Color _getTypeColor(EyewearType type) {
    switch (type) {
      case EyewearType.sunglasses:
        return Colors.orange;
      case EyewearType.prescription:
        return Colors.blue;
      case EyewearType.reading:
        return Colors.green;
      case EyewearType.sports:
        return Colors.red;
      case EyewearType.fashion:
        return Colors.purple;
    }
  }

  String _getTypeText(EyewearType type) {
    switch (type) {
      case EyewearType.sunglasses:
        return 'نظارات شمسية';
      case EyewearType.prescription:
        return 'نظارات طبية';
      case EyewearType.reading:
        return 'نظارات قراءة';
      case EyewearType.sports:
        return 'نظارات رياضية';
      case EyewearType.fashion:
        return 'نظارات أزياء';
    }
  }

  IconData _getGenderIcon(Gender gender) {
    switch (gender) {
      case Gender.men:
        return Icons.male;
      case Gender.women:
        return Icons.female;
      case Gender.unisex:
        return Icons.people;
      case Gender.kids:
        return Icons.child_care;
    }
  }

  Color _getGenderColor(Gender gender) {
    switch (gender) {
      case Gender.men:
        return Colors.blue;
      case Gender.women:
        return Colors.pink;
      case Gender.unisex:
        return Colors.purple;
      case Gender.kids:
        return Colors.green;
    }
  }

  String _getGenderText(Gender gender) {
    switch (gender) {
      case Gender.men:
        return 'رجالي';
      case Gender.women:
        return 'نسائي';
      case Gender.unisex:
        return 'للجنسين';
      case Gender.kids:
        return 'أطفال';
    }
  }

  // Image management methods
  void _addImageUrl() {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة صورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل رابط الصورة:'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'رابط الصورة',
                border: OutlineInputBorder(),
                hintText: 'https://example.com/image.jpg',
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    _imageUrls.add(value);
                  });
                  Get.back();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add default image for demo
              setState(() {
                _imageUrls.add('https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=400');
              });
              Get.back();
            },
            child: const Text('إضافة صورة تجريبية'),
          ),
        ],
      ),
    );
  }

  void _removeImageUrl(int index) {
    setState(() {
      _imageUrls.removeAt(index);
    });
  }

  // Features management methods
  void _addFeature() {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة ميزة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل الميزة الجديدة:'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'الميزة',
                border: OutlineInputBorder(),
                hintText: 'مثال: مقاوم للماء',
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty && !_features.contains(value)) {
                  setState(() {
                    _features.add(value);
                  });
                  Get.back();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add some default features for demo
              final defaultFeatures = ['مقاوم للماء', 'حماية من الأشعة فوق البنفسجية', 'خفيف الوزن'];
              for (final feature in defaultFeatures) {
                if (!_features.contains(feature)) {
                  setState(() {
                    _features.add(feature);
                  });
                  break;
                }
              }
              Get.back();
            },
            child: const Text('إضافة ميزة تجريبية'),
          ),
        ],
      ),
    );
  }

  void _removeFeature(String feature) {
    setState(() {
      _features.remove(feature);
    });
  }

  // Tags management methods
  void _addTag() {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة علامة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل العلامة الجديدة:'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'العلامة',
                border: OutlineInputBorder(),
                hintText: 'مثال: عصري',
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty && !_tags.contains(value)) {
                  setState(() {
                    _tags.add(value);
                  });
                  Get.back();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add some default tags for demo
              final defaultTags = ['عصري', 'كلاسيكي', 'رياضي', 'أنيق'];
              for (final tag in defaultTags) {
                if (!_tags.contains(tag)) {
                  setState(() {
                    _tags.add(tag);
                  });
                  break;
                }
              }
              Get.back();
            },
            child: const Text('إضافة علامة تجريبية'),
          ),
        ],
      ),
    );
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  // Save product method
  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Create EyewearSpecs
      final specs = EyewearSpecs(
        frameMaterial: FrameMaterial.plastic, // Default value
        lensType: LensType.regular, // Default value
        frameColor: 'أسود', // Default value
        lensColor: 'شفاف', // Default value
        frameSize: '52-18-140', // Default value
        lensWidth: double.tryParse(_lensWidthController.text) ?? 52.0,
        bridgeWidth: double.tryParse(_bridgeWidthController.text) ?? 18.0,
        templeLength: double.tryParse(_templeWidthController.text) ?? 140.0,
        hasUVProtection: _selectedType == EyewearType.sunglasses,
      );

      if (isEditing) {
        // Update existing product
        final updates = <String, dynamic>{
          'name': _nameController.text.trim(),
          'description': _descriptionController.text.trim(),
          'price': double.parse(_priceController.text),
          'category': _categoryController.text.trim(),
          'brand': _brandController.text.trim(),
          'stockQuantity': int.parse(_stockController.text),
          'type': _selectedType,
          'targetGender': _selectedGender,
          'specs': specs,
          'imageUrls': _imageUrls,
          'features': _features,
          'tags': _tags,
          'isActive': _isActive,
        };

        final adminService = Get.find<AdminService>();
        await adminService.updateProduct(widget.product!.id, updates);
        Get.back();
        Get.snackbar(
          'نجح',
          'تم تحديث المنتج بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        // Create new product - using AdminService directly since controller method doesn't exist
        final adminService = Get.find<AdminService>();
        await adminService.createProduct(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          category: _categoryController.text.trim(),
          imageUrls: _imageUrls,
          type: _selectedType,
          targetGender: _selectedGender,
          brand: _brandController.text.trim(),
          specs: specs,
          stockQuantity: int.parse(_stockController.text),
          features: _features,
          tags: _tags,
        );

        Get.back();
        Get.snackbar(
          'نجح',
          'تم إضافة المنتج بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ البيانات: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showDeleteConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المنتج "${widget.product!.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              setState(() {
                _isLoading = true;
              });

              await _adminController.deleteProduct(widget.product!.id);
              Get.back(); // العودة من صفحة التعديل
              Get.snackbar(
                'نجح',
                'تم حذف المنتج بنجاح',
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );

              setState(() {
                _isLoading = false;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}