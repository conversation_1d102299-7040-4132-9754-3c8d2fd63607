class CommissionSettings {
  final double defaultCommissionRate; // النسبة الافتراضية للعمولة
  final double minimumOrderAmount; // الحد الأدنى لقيمة الطلب للحصول على عمولة
  final int commissionApprovalDays; // عدد الأيام لاعتماد العمولة
  final double minimumPayoutAmount; // الحد الأدنى لصرف العمولات

  CommissionSettings({
    this.defaultCommissionRate = 0.10, // 10%
    this.minimumOrderAmount = 50.0,
    this.commissionApprovalDays = 7,
    this.minimumPayoutAmount = 100.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'defaultCommissionRate': defaultCommissionRate,
      'minimumOrderAmount': minimumOrderAmount,
      'commissionApprovalDays': commissionApprovalDays,
      'minimumPayoutAmount': minimumPayoutAmount,
    };
  }

  factory CommissionSettings.fromJson(Map<String, dynamic> json) {
    return CommissionSettings(
      defaultCommissionRate: (json['defaultCommissionRate'] ?? 0.10).toDouble(),
      minimumOrderAmount: (json['minimumOrderAmount'] ?? 50.0).toDouble(),
      commissionApprovalDays: json['commissionApprovalDays'] ?? 7,
      minimumPayoutAmount: (json['minimumPayoutAmount'] ?? 100.0).toDouble(),
    );
  }
}

class AppSettings {
  final String appName;
  final String appVersion;
  final String supportEmail;
  final String supportPhone;
  final CommissionSettings commissionSettings;
  final bool maintenanceMode;
  final String maintenanceMessage;
  final List<String> supportedCountries;
  final List<String> supportedCurrencies;
  final String defaultCurrency;
  final double shippingCost;
  final double freeShippingThreshold;
  final double taxRate;

  AppSettings({
    this.appName = 'متجر النظارات العصري',
    this.appVersion = '1.0.0',
    this.supportEmail = '<EMAIL>',
    this.supportPhone = '+213555123456',
    required this.commissionSettings,
    this.maintenanceMode = false,
    this.maintenanceMessage = 'التطبيق تحت الصيانة، يرجى المحاولة لاحقاً',
    this.supportedCountries = const ['DZ'], // الجزائر فقط
    this.supportedCurrencies = const ['DZD'], // الدينار الجزائري فقط
    this.defaultCurrency = 'DZD',
    this.shippingCost = 500.0, // 500 دج
    this.freeShippingThreshold = 15000.0, // 15000 دج للشحن المجاني
    this.taxRate = 0.19, // 19% ضريبة الجزائر
  });

  Map<String, dynamic> toJson() {
    return {
      'appName': appName,
      'appVersion': appVersion,
      'supportEmail': supportEmail,
      'supportPhone': supportPhone,
      'commissionSettings': commissionSettings.toJson(),
      'maintenanceMode': maintenanceMode,
      'maintenanceMessage': maintenanceMessage,
      'supportedCountries': supportedCountries,
      'supportedCurrencies': supportedCurrencies,
      'defaultCurrency': defaultCurrency,
      'shippingCost': shippingCost,
      'freeShippingThreshold': freeShippingThreshold,
      'taxRate': taxRate,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      appName: json['appName'] ?? 'متجر النظارات العصري',
      appVersion: json['appVersion'] ?? '1.0.0',
      supportEmail: json['supportEmail'] ?? '<EMAIL>',
      supportPhone: json['supportPhone'] ?? '+1234567890',
      commissionSettings: CommissionSettings.fromJson(json['commissionSettings'] ?? {}),
      maintenanceMode: json['maintenanceMode'] ?? false,
      maintenanceMessage: json['maintenanceMessage'] ?? 'التطبيق تحت الصيانة، يرجى المحاولة لاحقاً',
      supportedCountries: List<String>.from(json['supportedCountries'] ?? ['SA', 'AE', 'KW', 'QA', 'BH', 'OM']),
      supportedCurrencies: List<String>.from(json['supportedCurrencies'] ?? ['USD', 'SAR', 'AED']),
      defaultCurrency: json['defaultCurrency'] ?? 'USD',
      shippingCost: (json['shippingCost'] ?? 10.0).toDouble(),
      freeShippingThreshold: (json['freeShippingThreshold'] ?? 100.0).toDouble(),
      taxRate: (json['taxRate'] ?? 0.15).toDouble(),
    );
  }

  // إعدادات افتراضية
  static AppSettings get defaultSettings {
    return AppSettings(
      commissionSettings: CommissionSettings(),
    );
  }
}

// إعدادات تجريبية
final AppSettings demoAppSettings = AppSettings(
  commissionSettings: CommissionSettings(
    defaultCommissionRate: 0.10, // 10%
    minimumOrderAmount: 50.0,
    commissionApprovalDays: 7,
    minimumPayoutAmount: 100.0,
  ),
  shippingCost: 15.0,
  freeShippingThreshold: 150.0,
  taxRate: 0.15,
);
