import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Custom mouse event handler to prevent mouse tracker assertion errors
/// This wrapper helps manage mouse events more reliably on desktop platforms
class SafeMouseRegion extends StatefulWidget {
  final Widget child;
  final void Function(PointerEnterEvent)? onEnter;
  final void Function(PointerExitEvent)? onExit;
  final void Function(PointerHoverEvent)? onHover;
  final MouseCursor cursor;

  const SafeMouseRegion({
    super.key,
    required this.child,
    this.onEnter,
    this.onExit,
    this.onHover,
    this.cursor = MouseCursor.defer,
  });

  @override
  State<SafeMouseRegion> createState() => _SafeMouseRegionState();
}

class _SafeMouseRegionState extends State<SafeMouseRegion> {
  DateTime? _lastHoverEvent;
  static const Duration _hoverThrottle = Duration(milliseconds: 16); // ~60fps

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: widget.cursor,
      onEnter: _handleEnter,
      onExit: _handleExit,
      onHover: _handleHover,
      child: widget.child,
    );
  }

  void _handleEnter(PointerEnterEvent event) {
    if (!mounted) return;

    try {
      widget.onEnter?.call(event);
    } catch (e) {
      // Silently handle any assertion errors
      debugPrint('SafeMouseRegion: Enter event error handled: $e');
    }
  }

  void _handleExit(PointerExitEvent event) {
    if (!mounted) return;

    try {
      setState(() {
        _lastHoverEvent = null;
      });
      widget.onExit?.call(event);
    } catch (e) {
      // Silently handle any assertion errors
      debugPrint('SafeMouseRegion: Exit event error handled: $e');
    }
  }

  void _handleHover(PointerHoverEvent event) {
    if (!mounted) return;

    try {
      final now = DateTime.now();
      if (_lastHoverEvent != null &&
          now.difference(_lastHoverEvent!) < _hoverThrottle) {
        return; // Throttle hover events
      }

      _lastHoverEvent = now;
      widget.onHover?.call(event);
    } catch (e) {
      // Silently handle any assertion errors
      debugPrint('SafeMouseRegion: Hover event error handled: $e');
    }
  }
}

/// Safe gesture detector that handles mouse events more reliably
class SafeGestureDetector extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final void Function(TapDownDetails)? onTapDown;
  final void Function(TapUpDetails)? onTapUp;
  final HitTestBehavior? behavior;

  const SafeGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onTapDown,
    this.onTapUp,
    this.behavior,
  });

  @override
  State<SafeGestureDetector> createState() => _SafeGestureDetectorState();
}

class _SafeGestureDetectorState extends State<SafeGestureDetector> {
  DateTime? _lastTapTime;
  static const Duration _tapThrottle = Duration(milliseconds: 100);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: widget.behavior,
      onTap: _handleTap,
      onDoubleTap: widget.onDoubleTap,
      onLongPress: widget.onLongPress,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      child: widget.child,
    );
  }

  void _handleTap() {
    if (!mounted) return;

    try {
      final now = DateTime.now();
      if (_lastTapTime != null &&
          now.difference(_lastTapTime!) < _tapThrottle) {
        return; // Prevent rapid taps
      }

      _lastTapTime = now;
      widget.onTap?.call();
    } catch (e) {
      debugPrint('SafeGestureDetector: Tap event error handled: $e');
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (!mounted) return;

    try {
      widget.onTapDown?.call(details);
    } catch (e) {
      debugPrint('SafeGestureDetector: TapDown event error handled: $e');
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!mounted) return;

    try {
      widget.onTapUp?.call(details);
    } catch (e) {
      debugPrint('SafeGestureDetector: TapUp event error handled: $e');
    }
  }
}

/// Utility class for desktop-specific mouse handling
class DesktopMouseUtils {
  static bool get isDesktop {
    return Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.windows ||
           Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.macOS ||
           Theme.of(navigatorKey.currentContext!).platform == TargetPlatform.linux;
  }

  static Widget wrapWithSafeMouseHandling(Widget child) {
    if (!isDesktop) return child;

    return SafeMouseRegion(
      child: child,
    );
  }
}

// Global navigator key for accessing context
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
