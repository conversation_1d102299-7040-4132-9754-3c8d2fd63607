import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../models/product.dart';
import '../../config/desktop_config.dart';
import 'forms/add_edit_product_form.dart';

class AdminProductsScreen extends StatelessWidget {
  const AdminProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final adminController = Get.find<AdminController>();
    final isMobile = DesktopConfig.isMobileSize(context);
    final padding = DesktopConfig.getResponsivePadding(context);

    return Scaffold(
      body: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدارة المنتجات',
                      style: TextStyle(
                        fontSize: isMobile ? 24 : 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => Get.to(() => const AddEditProductForm()),
                        icon: const Icon(Icons.add_box),
                        label: const Text('إضافة منتج جديد'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'إدارة المنتجات',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => Get.to(() => const AddEditProductForm()),
                      icon: const Icon(Icons.add_box),
                      label: const Text('إضافة منتج جديد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),

            const SizedBox(height: 24),

            // Statistics Cards
            _buildStatisticsCards(adminController),

            const SizedBox(height: 24),

            // Filters and Search
            _buildFiltersSection(adminController),

            const SizedBox(height: 24),

            // Products Grid
            Expanded(
              child: _buildProductsGrid(adminController),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(AdminController controller) {
    return Obx(() {
      final analytics = controller.dashboardAnalytics;

      return Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المنتجات',
              '${analytics['totalProducts']}',
              Icons.inventory,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'متوفر في المخزون',
              '${analytics['inStockProducts']}',
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'نفد من المخزون',
              '${analytics['outOfStockProducts']}',
              Icons.remove_circle,
              Colors.red,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              'مخزون منخفض',
              '${analytics['lowStockProducts']}',
              Icons.warning,
              Colors.orange,
            ),
          ),
        ],
      );
    });
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection(AdminController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                // Search Field
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: controller.searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث في المنتجات',
                      hintText: 'ابحث بالاسم أو الوصف أو الفئة',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Category Filter
                Expanded(
                  child: Obx(() => DropdownButtonFormField<String>(
                    value: controller.selectedProductCategory.value.isEmpty
                        ? null
                        : controller.selectedProductCategory.value,
                    decoration: const InputDecoration(
                      labelText: 'تصفية حسب الفئة',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: [
                      const DropdownMenuItem<String>(
                        value: null,
                        child: Text('جميع الفئات'),
                      ),
                      ...controller.products
                          .map((p) => p.category)
                          .toSet()
                          .map((category) => DropdownMenuItem(
                                value: category,
                                child: Text(category),
                              )),
                    ],
                    onChanged: (value) {
                      controller.selectedProductCategory.value = value ?? '';
                    },
                  )),
                ),

                const SizedBox(width: 16),

                // Stock Filter
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Obx(() => CheckboxListTile(
                          title: const Text('متوفر فقط'),
                          value: controller.showInStockOnly.value,
                          onChanged: (value) {
                            controller.showInStockOnly.value = value ?? false;
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        )),
                      ),
                      Expanded(
                        child: Obx(() => CheckboxListTile(
                          title: const Text('مخزون منخفض'),
                          value: controller.showLowStockOnly.value,
                          onChanged: (value) {
                            controller.showLowStockOnly.value = value ?? false;
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        )),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Clear Filters Button
                ElevatedButton.icon(
                  onPressed: controller.clearProductFilters,
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الفلاتر'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsGrid(AdminController controller) {
    return Obx(() {
      final products = controller.products;

      if (controller.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (products.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد منتجات',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return _buildProductCard(controller, product);
        },
      );
    });
  }

  Widget _buildProductCard(AdminController controller, Product product) {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          Expanded(
            flex: 3,
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                    image: DecorationImage(
                      image: NetworkImage(product.imageUrls.first),
                      fit: BoxFit.cover,
                      onError: (error, stackTrace) {},
                    ),
                  ),
                  child: product.imageUrls.isEmpty
                      ? const Center(
                          child: Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
                        )
                      : null,
                ),

                // Status Badge
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: product.isActive ? Colors.green : Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      product.isActive ? 'نشط' : 'غير نشط',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // Stock Badge
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStockColor(product.stockQuantity),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${product.stockQuantity}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Product Info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Name
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Category and Brand
                  Text(
                    '${product.category} • ${product.brand}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Price
                  Text(
                    '${product.price.toStringAsFixed(0)} دج',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.green,
                    ),
                  ),

                  const Spacer(),

                  // Action Buttons
                  Builder(
                    builder: (context) {
                      final screenWidth = MediaQuery.of(context).size.width;
                      final isMobile = screenWidth < 600;
                      final iconSize = isMobile ? 24.0 : 18.0;

                      return Row(
                        children: [
                          Expanded(
                            child: IconButton(
                              onPressed: () => Get.to(() => AddEditProductForm(product: product)),
                              icon: Icon(Icons.edit, size: iconSize),
                              tooltip: 'تعديل',
                              color: Colors.blue,
                              padding: EdgeInsets.all(isMobile ? 12 : 8),
                            ),
                          ),
                          Expanded(
                            child: IconButton(
                              onPressed: () => _showStockDialog(controller, product),
                              icon: Icon(Icons.inventory, size: iconSize),
                              tooltip: 'إدارة المخزون',
                              color: Colors.orange,
                              padding: EdgeInsets.all(isMobile ? 12 : 8),
                            ),
                          ),
                          Expanded(
                            child: IconButton(
                              onPressed: () => _showDeleteConfirmation(controller, product),
                              icon: Icon(Icons.delete, size: iconSize),
                              tooltip: 'حذف',
                              color: Colors.red,
                              padding: EdgeInsets.all(isMobile ? 12 : 8),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getStockColor(int stock) {
    if (stock == 0) return Colors.red;
    if (stock <= 5) return Colors.orange;
    return Colors.green;
  }

  void _showStockDialog(AdminController controller, Product product) {
    final stockController = TextEditingController(text: product.stockQuantity.toString());

    Get.dialog(
      AlertDialog(
        title: Text('إدارة مخزون ${product.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الكمية الحالية: ${product.stockQuantity}'),
            const SizedBox(height: 16),
            TextField(
              controller: stockController,
              decoration: const InputDecoration(
                labelText: 'الكمية الجديدة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.inventory),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newStock = int.tryParse(stockController.text);
              if (newStock != null && newStock >= 0) {
                controller.updateProductStock(product.id, newStock);
                Get.back();
              } else {
                Get.snackbar('خطأ', 'يرجى إدخال كمية صحيحة');
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(AdminController controller, Product product) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف المنتج "${product.name}"؟\n\n'
          'هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة بهذا المنتج.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteProduct(product.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}