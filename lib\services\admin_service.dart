import 'package:get/get.dart';
import '../models/user.dart';
import '../models/product.dart';
import '../models/order.dart';
import '../models/app_settings.dart';
import '../models/affiliate_request.dart';
import 'auth_service.dart';

class AdminService extends GetxService {
  static AdminService get instance => Get.find();

  // Observable variables
  final RxList<User> _users = <User>[].obs;
  final RxList<Product> _products = <Product>[].obs;
  final RxList<Order> _orders = <Order>[].obs;
  final RxList<AffiliateRequest> _affiliateRequests = <AffiliateRequest>[].obs;
  final Rx<AppSettings> _appSettings = AppSettings.defaultSettings.obs;
  final RxBool _isLoading = false.obs;

  // Getters
  List<User> get users => _users;
  List<Product> get products => _products;
  List<Order> get orders => _orders;
  List<AffiliateRequest> get affiliateRequests => _affiliateRequests;
  AppSettings get appSettings => _appSettings.value;
  bool get isLoading => _isLoading.value;

  // Statistics getters
  int get totalUsers => _users.length;
  int get totalCustomers => _users.where((u) => u.isCustomer).length;
  int get totalAffiliates => _users.where((u) => u.isAffiliate).length;
  int get totalAdmins => _users.where((u) => u.isAdmin).length;
  int get activeUsers => _users.where((u) => u.isActive).length;
  int get suspendedUsers => _users.where((u) => u.status == UserStatus.suspended).length;

  int get totalProducts => _products.length;
  int get inStockProducts => _products.where((p) => p.stockQuantity > 0).length;
  int get outOfStockProducts => _products.where((p) => p.stockQuantity == 0).length;
  int get lowStockProducts => _products.where((p) => p.stockQuantity > 0 && p.stockQuantity <= 5).length;

  int get totalOrders => _orders.length;
  int get pendingOrders => _orders.where((o) => o.isPending).length;
  int get confirmedOrders => _orders.where((o) => o.isConfirmed).length;
  int get shippedOrders => _orders.where((o) => o.isShipped).length;
  int get deliveredOrders => _orders.where((o) => o.isDelivered).length;
  int get cancelledOrders => _orders.where((o) => o.isCancelled).length;

  double get totalRevenue => _orders.where((o) => o.isPaid).fold(0.0, (sum, o) => sum + o.totalAmount);
  double get monthlyRevenue {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return _orders
        .where((o) => o.isPaid && o.createdAt.isAfter(startOfMonth))
        .fold(0.0, (sum, o) => sum + o.totalAmount);
  }

  int get pendingAffiliateRequests => _affiliateRequests.where((r) => r.status == AffiliateRequestStatus.pending).length;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  // Initialize demo data
  void _initializeData() {
    _isLoading.value = true;

    // Load users from AuthService
    final authService = Get.find<AuthService>();
    _users.assignAll(authService.getAllUsers());

    // Load demo products
    _products.assignAll(Product.demoProducts);

    // Load demo orders
    _orders.assignAll(_generateDemoOrders());

    // Load demo affiliate requests
    _affiliateRequests.assignAll(_generateDemoAffiliateRequests());

    // Load app settings
    _appSettings.value = AppSettings.defaultSettings;

    _isLoading.value = false;
  }

  // User Management Methods
  Future<bool> createUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
    required UserRole role,
    UserStatus status = UserStatus.active,
  }) async {
    try {
      _isLoading.value = true;

      // Check if email already exists
      if (_users.any((u) => u.email.toLowerCase() == email.toLowerCase())) {
        Get.snackbar('خطأ', 'البريد الإلكتروني مستخدم بالفعل');
        return false;
      }

      final newUser = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        role: role,
        status: status,
        createdAt: DateTime.now(),
        affiliateCode: role == UserRole.affiliate
            ? _generateAffiliateCode(firstName, lastName)
            : null,
      );

      _users.add(newUser);

      Get.snackbar('نجح', 'تم إنشاء المستخدم بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إنشاء المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      _isLoading.value = true;

      final userIndex = _users.indexWhere((u) => u.id == userId);
      if (userIndex == -1) {
        Get.snackbar('خطأ', 'المستخدم غير موجود');
        return false;
      }

      final user = _users[userIndex];
      final updatedUser = User(
        id: user.id,
        email: updates['email'] ?? user.email,
        password: user.password,
        firstName: updates['firstName'] ?? user.firstName,
        lastName: updates['lastName'] ?? user.lastName,
        phoneNumber: updates['phoneNumber'] ?? user.phoneNumber,
        profileImageUrl: updates['profileImageUrl'] ?? user.profileImageUrl,
        role: updates['role'] ?? user.role,
        status: updates['status'] ?? user.status,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        affiliateCode: user.affiliateCode,
        totalEarnings: user.totalEarnings,
        pendingEarnings: user.pendingEarnings,
        totalReferrals: user.totalReferrals,
        referredBy: user.referredBy,
        totalSpent: user.totalSpent,
        totalOrders: user.totalOrders,
      );

      _users[userIndex] = updatedUser;

      Get.snackbar('نجح', 'تم تحديث المستخدم بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    try {
      _isLoading.value = true;

      final userIndex = _users.indexWhere((u) => u.id == userId);
      if (userIndex == -1) {
        Get.snackbar('خطأ', 'المستخدم غير موجود');
        return false;
      }

      _users.removeAt(userIndex);

      Get.snackbar('نجح', 'تم حذف المستخدم بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في حذف المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> suspendUser(String userId) async {
    return updateUser(userId, {'status': UserStatus.suspended});
  }

  Future<bool> activateUser(String userId) async {
    return updateUser(userId, {'status': UserStatus.active});
  }

  List<User> searchUsers(String query) {
    if (query.isEmpty) return _users;

    return _users.where((user) {
      return user.fullName.toLowerCase().contains(query.toLowerCase()) ||
             user.email.toLowerCase().contains(query.toLowerCase()) ||
             (user.phoneNumber?.contains(query) ?? false);
    }).toList();
  }

  List<User> filterUsersByRole(UserRole? role) {
    if (role == null) return _users;
    return _users.where((user) => user.role == role).toList();
  }

  List<User> filterUsersByStatus(UserStatus? status) {
    if (status == null) return _users;
    return _users.where((user) => user.status == status).toList();
  }

  // Generate affiliate code
  String _generateAffiliateCode(String firstName, String lastName) {
    final initials = '${firstName[0]}${lastName[0]}'.toUpperCase();
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    return '$initials$timestamp';
  }

  // Generate demo orders
  List<Order> _generateDemoOrders() {
    final demoOrders = <Order>[];
    final customers = _users.where((u) => u.isCustomer).toList();
    final products = Product.demoProducts;

    for (int i = 0; i < 50; i++) {
      final customer = customers.isNotEmpty ? customers[i % customers.length] : _createDemoCustomer(i);
      final orderItems = <OrderItem>[];
      final numItems = (i % 3) + 1;

      double subtotal = 0;
      for (int j = 0; j < numItems; j++) {
        final product = products[j % products.length];
        final quantity = (j % 2) + 1;
        final totalPrice = product.price * quantity;
        subtotal += totalPrice;

        orderItems.add(OrderItem(
          productId: product.id.toString(),
          productName: product.name,
          productImage: product.imageUrls.isNotEmpty ? product.imageUrls.first : '',
          quantity: quantity,
          unitPrice: product.price,
          totalPrice: totalPrice,
        ));
      }

      final shippingCost = subtotal > 15000 ? 0.0 : 500.0;
      final tax = subtotal * 0.19;
      final totalAmount = subtotal + shippingCost + tax;

      final statuses = OrderStatus.values;
      final paymentStatuses = PaymentStatus.values;

      demoOrders.add(Order(
        id: 'order_${DateTime.now().millisecondsSinceEpoch}_$i',
        customerId: customer.id,
        affiliateId: i % 4 == 0 && _users.any((u) => u.isAffiliate)
            ? _users.firstWhere((u) => u.isAffiliate).id
            : null,
        items: orderItems,
        subtotal: subtotal,
        shippingCost: shippingCost,
        tax: tax,
        totalAmount: totalAmount,
        status: statuses[i % statuses.length],
        paymentStatus: paymentStatuses[i % paymentStatuses.length],
        shippingAddress: ShippingAddress(
          fullName: customer.fullName,
          phoneNumber: customer.phoneNumber ?? '+213555123456',
          addressLine1: 'شارع ${i + 1}',
          city: 'الجزائر',
          state: 'الجزائر',
          postalCode: '16000',
          country: 'الجزائر',
        ),
        createdAt: DateTime.now().subtract(Duration(days: i)),
        trackingNumber: i % 3 == 0 ? 'TRK${DateTime.now().millisecondsSinceEpoch}' : null,
      ));
    }

    return demoOrders;
  }

  // Generate demo affiliate requests
  List<AffiliateRequest> _generateDemoAffiliateRequests() {
    final customers = _users.where((u) => u.isCustomer).take(10).toList();
    final requests = <AffiliateRequest>[];

    for (int i = 0; i < customers.length; i++) {
      final customer = customers[i];
      final statuses = AffiliateRequestStatus.values;

      requests.add(AffiliateRequest(
        id: 'req_${DateTime.now().millisecondsSinceEpoch}_$i',
        userId: customer.id,
        userFullName: customer.fullName,
        userEmail: customer.email,
        userPhone: customer.phoneNumber ?? '+213555123456',
        reason: 'أريد أن أصبح مسوقاً لكسب دخل إضافي',
        experience: i % 3 == 0 ? 'خبرة في التسويق الرقمي' : 'مبتدئ في التسويق',
        socialMediaLinks: 'https://facebook.com/user$i, https://instagram.com/user$i',
        status: statuses[i % statuses.length],
        requestDate: DateTime.now().subtract(Duration(days: i)),
        reviewDate: i % 2 == 0 ? DateTime.now().subtract(Duration(days: i - 1)) : null,
        reviewedBy: i % 2 == 0 ? 'admin_001' : null,
        reviewNotes: i % 2 == 0 ? 'تمت المراجعة والموافقة' : null,
      ));
    }

    return requests;
  }

  // Create demo customer for orders
  User _createDemoCustomer(int index) {
    return User(
      id: 'demo_customer_$index',
      email: 'customer$<EMAIL>',
      password: '123456',
      firstName: 'عميل',
      lastName: '$index',
      phoneNumber: '+21355512345$index',
      role: UserRole.customer,
      createdAt: DateTime.now().subtract(Duration(days: index)),
    );
  }

  // Product Management Methods
  Future<bool> createProduct({
    required String name,
    required String description,
    required double price,
    required String category,
    required List<String> imageUrls,
    required EyewearType type,
    required Gender targetGender,
    required String brand,
    required EyewearSpecs specs,
    int stockQuantity = 0,
    List<String> features = const [],
    List<String> tags = const [],
  }) async {
    try {
      _isLoading.value = true;

      final newProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch,
        name: name,
        description: description,
        price: price,
        category: category,
        imageUrls: imageUrls,
        type: type,
        targetGender: targetGender,
        brand: brand,
        specs: specs,
        stockQuantity: stockQuantity,
        features: features,
        tags: tags,
        createdAt: DateTime.now(),
      );

      _products.add(newProduct);

      Get.snackbar('نجح', 'تم إنشاء المنتج بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إنشاء المنتج: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateProduct(int productId, Map<String, dynamic> updates) async {
    try {
      _isLoading.value = true;

      final productIndex = _products.indexWhere((p) => p.id == productId);
      if (productIndex == -1) {
        Get.snackbar('خطأ', 'المنتج غير موجود');
        return false;
      }

      final product = _products[productIndex];
      final updatedProduct = Product(
        id: product.id,
        name: updates['name'] ?? product.name,
        description: updates['description'] ?? product.description,
        price: updates['price'] ?? product.price,
        category: updates['category'] ?? product.category,
        imageUrls: updates['imageUrls'] ?? product.imageUrls,
        type: updates['type'] ?? product.type,
        targetGender: updates['targetGender'] ?? product.targetGender,
        brand: updates['brand'] ?? product.brand,
        specs: updates['specs'] ?? product.specs,
        stockQuantity: updates['stockQuantity'] ?? product.stockQuantity,
        features: updates['features'] ?? product.features,
        tags: updates['tags'] ?? product.tags,
        isActive: updates['isActive'] ?? product.isActive,
        createdAt: product.createdAt,
      );

      _products[productIndex] = updatedProduct;

      Get.snackbar('نجح', 'تم تحديث المنتج بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث المنتج: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> deleteProduct(int productId) async {
    try {
      _isLoading.value = true;

      final productIndex = _products.indexWhere((p) => p.id == productId);
      if (productIndex == -1) {
        Get.snackbar('خطأ', 'المنتج غير موجود');
        return false;
      }

      _products.removeAt(productIndex);

      Get.snackbar('نجح', 'تم حذف المنتج بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في حذف المنتج: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateProductStock(int productId, int newStock) async {
    return updateProduct(productId, {'stockQuantity': newStock});
  }

  List<Product> searchProducts(String query) {
    if (query.isEmpty) return _products;

    return _products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
             product.description.toLowerCase().contains(query.toLowerCase()) ||
             product.category.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  List<Product> filterProductsByCategory(String? category) {
    if (category == null || category.isEmpty) return _products;
    return _products.where((product) => product.category == category).toList();
  }

  List<Product> getProductsByStockStatus({bool? inStock, bool? lowStock}) {
    return _products.where((product) {
      if (inStock == true && product.stockQuantity <= 0) return false;
      if (inStock == false && product.stockQuantity > 0) return false;
      if (lowStock == true && (product.stockQuantity <= 0 || product.stockQuantity > 5)) return false;
      if (lowStock == false && product.stockQuantity > 0 && product.stockQuantity <= 5) return false;
      return true;
    }).toList();
  }

  // Order Management Methods
  Future<bool> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      _isLoading.value = true;

      final orderIndex = _orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) {
        Get.snackbar('خطأ', 'الطلب غير موجود');
        return false;
      }

      final order = _orders[orderIndex];
      final updatedOrder = Order(
        id: order.id,
        customerId: order.customerId,
        affiliateId: order.affiliateId,
        items: order.items,
        subtotal: order.subtotal,
        shippingCost: order.shippingCost,
        tax: order.tax,
        discount: order.discount,
        totalAmount: order.totalAmount,
        status: newStatus,
        paymentStatus: order.paymentStatus,
        shippingAddress: order.shippingAddress,
        createdAt: order.createdAt,
        confirmedAt: newStatus == OrderStatus.confirmed && order.confirmedAt == null
            ? DateTime.now()
            : order.confirmedAt,
        shippedAt: newStatus == OrderStatus.shipped && order.shippedAt == null
            ? DateTime.now()
            : order.shippedAt,
        deliveredAt: newStatus == OrderStatus.delivered && order.deliveredAt == null
            ? DateTime.now()
            : order.deliveredAt,
        trackingNumber: order.trackingNumber,
        notes: order.notes,
      );

      _orders[orderIndex] = updatedOrder;

      Get.snackbar('نجح', 'تم تحديث حالة الطلب بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث حالة الطلب: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updatePaymentStatus(String orderId, PaymentStatus newStatus) async {
    try {
      _isLoading.value = true;

      final orderIndex = _orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) {
        Get.snackbar('خطأ', 'الطلب غير موجود');
        return false;
      }

      final order = _orders[orderIndex];
      final updatedOrder = Order(
        id: order.id,
        customerId: order.customerId,
        affiliateId: order.affiliateId,
        items: order.items,
        subtotal: order.subtotal,
        shippingCost: order.shippingCost,
        tax: order.tax,
        discount: order.discount,
        totalAmount: order.totalAmount,
        status: order.status,
        paymentStatus: newStatus,
        shippingAddress: order.shippingAddress,
        createdAt: order.createdAt,
        confirmedAt: order.confirmedAt,
        shippedAt: order.shippedAt,
        deliveredAt: order.deliveredAt,
        trackingNumber: order.trackingNumber,
        notes: order.notes,
      );

      _orders[orderIndex] = updatedOrder;

      Get.snackbar('نجح', 'تم تحديث حالة الدفع بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث حالة الدفع: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> addTrackingNumber(String orderId, String trackingNumber) async {
    try {
      _isLoading.value = true;

      final orderIndex = _orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) {
        Get.snackbar('خطأ', 'الطلب غير موجود');
        return false;
      }

      final order = _orders[orderIndex];
      final updatedOrder = Order(
        id: order.id,
        customerId: order.customerId,
        affiliateId: order.affiliateId,
        items: order.items,
        subtotal: order.subtotal,
        shippingCost: order.shippingCost,
        tax: order.tax,
        discount: order.discount,
        totalAmount: order.totalAmount,
        status: order.status,
        paymentStatus: order.paymentStatus,
        shippingAddress: order.shippingAddress,
        createdAt: order.createdAt,
        confirmedAt: order.confirmedAt,
        shippedAt: order.shippedAt,
        deliveredAt: order.deliveredAt,
        trackingNumber: trackingNumber,
        notes: order.notes,
      );

      _orders[orderIndex] = updatedOrder;

      Get.snackbar('نجح', 'تم إضافة رقم التتبع بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إضافة رقم التتبع: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  List<Order> searchOrders(String query) {
    if (query.isEmpty) return _orders;

    return _orders.where((order) {
      return order.id.toLowerCase().contains(query.toLowerCase()) ||
             order.trackingNumber?.toLowerCase().contains(query.toLowerCase()) == true ||
             order.shippingAddress.fullName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  List<Order> filterOrdersByStatus(OrderStatus? status) {
    if (status == null) return _orders;
    return _orders.where((order) => order.status == status).toList();
  }

  List<Order> filterOrdersByPaymentStatus(PaymentStatus? status) {
    if (status == null) return _orders;
    return _orders.where((order) => order.paymentStatus == status).toList();
  }

  List<Order> getOrdersByDateRange(DateTime startDate, DateTime endDate) {
    return _orders.where((order) {
      return order.createdAt.isAfter(startDate) && order.createdAt.isBefore(endDate);
    }).toList();
  }

  // Affiliate Request Management Methods
  Future<bool> approveAffiliateRequest(String requestId, String adminId, {String? notes}) async {
    try {
      _isLoading.value = true;

      final requestIndex = _affiliateRequests.indexWhere((r) => r.id == requestId);
      if (requestIndex == -1) {
        Get.snackbar('خطأ', 'الطلب غير موجود');
        return false;
      }

      final request = _affiliateRequests[requestIndex];

      // Update the request status
      final updatedRequest = AffiliateRequest(
        id: request.id,
        userId: request.userId,
        userFullName: request.userFullName,
        userEmail: request.userEmail,
        userPhone: request.userPhone,
        reason: request.reason,
        experience: request.experience,
        socialMediaLinks: request.socialMediaLinks,
        status: AffiliateRequestStatus.approved,
        requestDate: request.requestDate,
        reviewDate: DateTime.now(),
        reviewedBy: adminId,
        reviewNotes: notes ?? 'تمت الموافقة على الطلب',
      );

      _affiliateRequests[requestIndex] = updatedRequest;

      // Convert user to affiliate
      final userIndex = _users.indexWhere((u) => u.id == request.userId);
      if (userIndex != -1) {
        final user = _users[userIndex];
        final updatedUser = User(
          id: user.id,
          email: user.email,
          password: user.password,
          firstName: user.firstName,
          lastName: user.lastName,
          phoneNumber: user.phoneNumber,
          profileImageUrl: user.profileImageUrl,
          role: UserRole.affiliate,
          status: user.status,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
          affiliateCode: _generateAffiliateCode(user.firstName, user.lastName),
          totalEarnings: 0.0,
          pendingEarnings: 0.0,
          totalReferrals: 0,
          referredBy: user.referredBy,
          totalSpent: user.totalSpent,
          totalOrders: user.totalOrders,
        );
        _users[userIndex] = updatedUser;
      }

      Get.snackbar('نجح', 'تمت الموافقة على طلب التسويق بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في الموافقة على الطلب: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> rejectAffiliateRequest(String requestId, String adminId, {String? notes}) async {
    try {
      _isLoading.value = true;

      final requestIndex = _affiliateRequests.indexWhere((r) => r.id == requestId);
      if (requestIndex == -1) {
        Get.snackbar('خطأ', 'الطلب غير موجود');
        return false;
      }

      final request = _affiliateRequests[requestIndex];

      final updatedRequest = AffiliateRequest(
        id: request.id,
        userId: request.userId,
        userFullName: request.userFullName,
        userEmail: request.userEmail,
        userPhone: request.userPhone,
        reason: request.reason,
        experience: request.experience,
        socialMediaLinks: request.socialMediaLinks,
        status: AffiliateRequestStatus.rejected,
        requestDate: request.requestDate,
        reviewDate: DateTime.now(),
        reviewedBy: adminId,
        reviewNotes: notes ?? 'تم رفض الطلب',
      );

      _affiliateRequests[requestIndex] = updatedRequest;

      Get.snackbar('نجح', 'تم رفض طلب التسويق');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في رفض الطلب: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // App Settings Management
  Future<bool> updateAppSettings(AppSettings newSettings) async {
    try {
      _isLoading.value = true;
      _appSettings.value = newSettings;
      Get.snackbar('نجح', 'تم تحديث إعدادات التطبيق بنجاح');
      return true;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Analytics and Reports
  Map<String, dynamic> getDashboardAnalytics() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    return {
      'totalUsers': totalUsers,
      'totalCustomers': totalCustomers,
      'totalAffiliates': totalAffiliates,
      'activeUsers': activeUsers,
      'totalProducts': totalProducts,
      'inStockProducts': inStockProducts,
      'outOfStockProducts': outOfStockProducts,
      'lowStockProducts': lowStockProducts,
      'totalOrders': totalOrders,
      'pendingOrders': pendingOrders,
      'totalRevenue': totalRevenue,
      'monthlyRevenue': monthlyRevenue,
      'weeklyRevenue': _orders
          .where((o) => o.isPaid && o.createdAt.isAfter(startOfWeek))
          .fold(0.0, (sum, o) => sum + o.totalAmount),
      'pendingAffiliateRequests': pendingAffiliateRequests,
      'monthlyOrders': _orders
          .where((o) => o.createdAt.isAfter(startOfMonth))
          .length,
      'weeklyOrders': _orders
          .where((o) => o.createdAt.isAfter(startOfWeek))
          .length,
    };
  }

  List<Map<String, dynamic>> getSalesReport(DateTime startDate, DateTime endDate) {
    final ordersInRange = getOrdersByDateRange(startDate, endDate);
    final report = <Map<String, dynamic>>[];

    for (final order in ordersInRange) {
      report.add({
        'orderId': order.id,
        'customerName': order.shippingAddress.fullName,
        'totalAmount': order.totalAmount,
        'status': order.status.toString(),
        'paymentStatus': order.paymentStatus.toString(),
        'createdAt': order.createdAt,
        'itemCount': order.totalItems,
      });
    }

    return report;
  }

  Map<String, int> getProductCategoryStats() {
    final categoryStats = <String, int>{};

    for (final product in _products) {
      categoryStats[product.category] = (categoryStats[product.category] ?? 0) + 1;
    }

    return categoryStats;
  }

  Map<String, double> getRevenueByCategoryReport() {
    final categoryRevenue = <String, double>{};

    for (final order in _orders.where((o) => o.isPaid)) {
      for (final item in order.items) {
        final product = _products.firstWhere(
          (p) => p.id.toString() == item.productId,
          orElse: () => _products.first,
        );
        categoryRevenue[product.category] =
            (categoryRevenue[product.category] ?? 0.0) + item.totalPrice;
      }
    }

    return categoryRevenue;
  }
}
