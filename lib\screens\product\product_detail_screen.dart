import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/product.dart';
import '../../widgets/user_role_widgets.dart';
import '../../widgets/product_detail_widgets.dart';

class ProductDetailScreen extends StatelessWidget {
  final Product product;

  const ProductDetailScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(product.name),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _shareProduct(),
            icon: const Icon(Icons.share),
            tooltip: 'مشاركة',
          ),
          IconButton(
            onPressed: () => _addToWishlist(),
            icon: const Icon(Icons.favorite_border),
            tooltip: 'إضافة للمفضلة',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // معرض الصور
            Padding(
              padding: const EdgeInsets.all(16),
              child: ProductImageGallery(product: product),
            ),

            // معلومات المنتج الأساسية
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ProductBasicInfo(product: product),
            ),

            const SizedBox(height: 24),

            // أزرار الإجراءات
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ProductActionButtons(product: product),
            ),

            const SizedBox(height: 24),

            // معلومات الشحن والإرجاع
            _buildShippingInfo(),

            const SizedBox(height: 24),

            // تفاصيل المنتج المتقدمة
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ProductAdvancedDetails(product: product),
            ),

            const SizedBox(height: 24),

            // المراجعات
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ProductReviews(product: product),
            ),

            const SizedBox(height: 24),

            // منتجات مشابهة
            _buildSimilarProducts(),

            const SizedBox(height: 24),
          ],
        ),
      ),
      // شريط سفلي ثابت للإجراءات السريعة
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildShippingInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.local_shipping, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'الشحن والتوصيل',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.payment, 'الدفع عند الاستلام'),
          _buildInfoRow(Icons.schedule, 'التوصيل خلال 2-5 أيام عمل'),
          _buildInfoRow(Icons.monetization_on, 'شحن مجاني للطلبات فوق 15,000 دج'),
          _buildInfoRow(Icons.cached, 'إمكانية الإرجاع خلال 14 يوم'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimilarProducts() {
    // منتجات مشابهة (يمكن تحسينها لاحقاً)
    final similarProducts = Product.demoProducts.where((p) => p.id != product.id).take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'منتجات مشابهة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: similarProducts.length,
            itemBuilder: (context, index) {
              final similarProduct = similarProducts[index];
              return GestureDetector(
                onTap: () => Get.to(() => ProductDetailScreen(product: similarProduct)),
                child: Container(
                  width: 150,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                          child: Image.network(
                            similarProduct.mainImageUrl,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.shade200,
                                child: const Icon(Icons.image_not_supported),
                              );
                            },
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              similarProduct.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              similarProduct.formattedPrice,
                              style: const TextStyle(
                                color: Colors.teal,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // السعر
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'السعر',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
              Text(
                product.formattedPrice,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
          // أزرار الإجراءات
          Expanded(
            child: ProductActionButtons(product: product),
          ),
        ],
      ),
    );
  }

  void _shareProduct() {
    Get.snackbar('مشاركة', 'تم نسخ رابط المنتج');
  }

  void _addToWishlist() {
    Get.snackbar('المفضلة', 'تم إضافة المنتج للمفضلة');
  }
}
