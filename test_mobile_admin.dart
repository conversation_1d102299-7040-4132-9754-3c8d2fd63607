import 'package:flutter/material.dart';

void main() {
  runApp(const TestMobileAdminApp());
}

class TestMobileAdminApp extends StatelessWidget {
  const TestMobileAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Mobile Admin Dashboard',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        useMaterial3: true,
        fontFamily: 'Arial',
      ),
      home: const TestMobileAdminDashboard(),
    );
  }
}

class TestMobileAdminDashboard extends StatelessWidget {
  const TestMobileAdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار لوحة تحكم الإدارة المتجاوبة'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      drawer: _buildMobileDrawer(),
      body: _buildResponsiveContent(context),
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      child: Column(
        children: [
          const SizedBox(height: 20),
          // Admin Profile
          Container(
            padding: const EdgeInsets.all(16),
            child: const Column(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.red,
                  child: Icon(Icons.admin_panel_settings, color: Colors.white, size: 30),
                ),
                SizedBox(height: 8),
                Text(
                  'مدير النظام',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                Text(
                  '<EMAIL>',
                  style: TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ),
          const Divider(),
          // Navigation Items
          Expanded(
            child: ListView(
              children: [
                _buildNavItem('لوحة التحكم', Icons.dashboard, true),
                _buildNavItem('إدارة المستخدمين', Icons.people, false),
                _buildNavItem('إدارة المنتجات', Icons.inventory, false),
                _buildNavItem('إدارة الطلبات', Icons.shopping_cart, false),
                _buildNavItem('طلبات المسوقين', Icons.group_add, false),
                _buildNavItem('التقارير', Icons.analytics, false),
                _buildNavItem('الإعدادات', Icons.settings, false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(String title, IconData icon, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withOpacity(0.1) : null,
        borderRadius: BorderRadius.circular(8),
        border: isSelected ? Border.all(color: Colors.blue.withOpacity(0.3)) : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey[600],
          size: 24,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 16,
          ),
        ),
        onTap: () {},
      ),
    );
  }

  Widget _buildResponsiveContent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(isMobile ? 16 : 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'لوحة التحكم الرئيسية',
            style: TextStyle(
              fontSize: isMobile ? 24 : 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          // Statistics Cards
          _buildStatsCards(isMobile),
          
          const SizedBox(height: 32),
          
          // Quick Actions
          _buildQuickActions(isMobile),
        ],
      ),
    );
  }

  Widget _buildStatsCards(bool isMobile) {
    final statsData = [
      {'title': 'إجمالي المستخدمين', 'value': '1,234', 'icon': Icons.people, 'color': Colors.blue},
      {'title': 'إجمالي المنتجات', 'value': '567', 'icon': Icons.inventory, 'color': Colors.green},
      {'title': 'إجمالي الطلبات', 'value': '890', 'icon': Icons.shopping_cart, 'color': Colors.orange},
      {'title': 'إجمالي المبيعات', 'value': '12,345 دج', 'icon': Icons.attach_money, 'color': Colors.purple},
    ];

    if (isMobile) {
      return Column(
        children: [
          for (int i = 0; i < statsData.length; i += 2)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(child: _buildStatCard(statsData[i], isMobile)),
                    if (i + 1 < statsData.length) ...[
                      const SizedBox(width: 12),
                      Expanded(child: _buildStatCard(statsData[i + 1], isMobile)),
                    ] else
                      const Expanded(child: SizedBox()),
                  ],
                ),
              ),
            ),
        ],
      );
    } else {
      return IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: statsData.map((stat) => 
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(right: 16),
                child: _buildStatCard(stat, isMobile),
              ),
            ),
          ).toList(),
        ),
      );
    }
  }

  Widget _buildStatCard(Map<String, dynamic> stat, bool isMobile) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.all(isMobile ? 8 : 12),
                  decoration: BoxDecoration(
                    color: (stat['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                  ),
                  child: Icon(
                    stat['icon'] as IconData,
                    color: stat['color'] as Color,
                    size: isMobile ? 20 : 28,
                  ),
                ),
                Icon(
                  Icons.more_vert,
                  color: Colors.grey[400],
                  size: isMobile ? 18 : 24,
                ),
              ],
            ),
            SizedBox(height: isMobile ? 12 : 16),
            Text(
              stat['value'] as String,
              style: TextStyle(
                fontSize: isMobile ? 20 : 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              stat['title'] as String,
              style: TextStyle(
                fontSize: isMobile ? 12 : 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(bool isMobile) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: isMobile ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: isMobile ? 12 : 16),
            _buildQuickActionButton('إضافة مستخدم جديد', Icons.person_add, Colors.blue, isMobile),
            SizedBox(height: isMobile ? 8 : 12),
            _buildQuickActionButton('إضافة منتج جديد', Icons.add_box, Colors.green, isMobile),
            SizedBox(height: isMobile ? 8 : 12),
            _buildQuickActionButton('عرض التقارير', Icons.analytics, Colors.orange, isMobile),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String title, IconData icon, Color color, bool isMobile) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.all(isMobile ? 16 : 12),
        decoration: BoxDecoration(
          border: Border.all(color: color.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(isMobile ? 12 : 8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: color,
                size: isMobile ? 24 : 20,
              ),
            ),
            SizedBox(width: isMobile ? 16 : 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: isMobile ? 16 : 14,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: isMobile ? 20 : 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
