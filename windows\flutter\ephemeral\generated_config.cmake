# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Program Files\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\my-ecommerce-app\\my_ecommerce_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Program Files\\flutter"
  "PROJECT_DIR=C:\\my-ecommerce-app\\my_ecommerce_app"
  "FLUTTER_ROOT=C:\\Program Files\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\my-ecommerce-app\\my_ecommerce_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\my-ecommerce-app\\my_ecommerce_app"
  "FLUTTER_TARGET=C:\\my-ecommerce-app\\my_ecommerce_app\\test_mobile_admin.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8yZTRiYTljNmZiNDk5Y2NkNGU4MTQyMDU0Mzc4M2NjNzI2N2FlNDA2Lw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\my-ecommerce-app\\my_ecommerce_app\\.dart_tool\\package_config.json"
)
